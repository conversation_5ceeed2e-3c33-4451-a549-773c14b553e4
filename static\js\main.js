// XCapital Frontend JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips and interactive elements
    initializeTooltips();
    initializeCounters();
    initializeCharts();
    initializeClipboard();
    
    // Auto-hide alerts after 5 seconds
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            alert.style.opacity = '0';
            alert.style.transform = 'translateY(-20px)';
            setTimeout(() => alert.remove(), 300);
        });
    }, 5000);
});

// Initialize tooltips
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(event) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = event.target.getAttribute('data-tooltip');
    tooltip.style.cssText = `
        position: absolute;
        background: var(--card-grey);
        color: var(--text-white);
        padding: 0.5rem 1rem;
        border-radius: 5px;
        font-size: 0.9rem;
        z-index: 1000;
        pointer-events: none;
        box-shadow: var(--shadow-glow);
        border: 1px solid var(--primary-green);
    `;
    
    document.body.appendChild(tooltip);
    
    const rect = event.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
    
    event.target.tooltip = tooltip;
}

function hideTooltip(event) {
    if (event.target.tooltip) {
        event.target.tooltip.remove();
        delete event.target.tooltip;
    }
}

// Animated counters for statistics
function initializeCounters() {
    const counters = document.querySelectorAll('.stat-value');
    
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    counters.forEach(counter => observer.observe(counter));
}

function animateCounter(element) {
    const target = parseFloat(element.textContent.replace(/[^0-9.]/g, ''));
    const duration = 2000;
    const increment = target / (duration / 16);
    let current = 0;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        
        // Format number with currency symbol if present
        const originalText = element.textContent;
        const hasUSDT = originalText.includes('USDT');
        const hasPercent = originalText.includes('%');
        
        let formattedValue = current.toFixed(current >= 1000 ? 0 : 2);
        if (hasUSDT) formattedValue += ' USDT';
        if (hasPercent) formattedValue += '%';
        
        element.textContent = formattedValue;
    }, 16);
}

// Initialize charts if Chart.js is available
function initializeCharts() {
    if (typeof Chart === 'undefined') return;
    
    // Earnings chart
    const earningsCtx = document.getElementById('earningsChart');
    if (earningsCtx) {
        new Chart(earningsCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Daily Earnings',
                    data: [],
                    borderColor: '#00D4AA',
                    backgroundColor: 'rgba(0, 212, 170, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: '#FFFFFF'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#FFFFFF'
                        },
                        grid: {
                            color: 'rgba(0, 212, 170, 0.1)'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#FFFFFF'
                        },
                        grid: {
                            color: 'rgba(0, 212, 170, 0.1)'
                        }
                    }
                }
            }
        });
    }
    
    // Referral chart
    const referralCtx = document.getElementById('referralChart');
    if (referralCtx) {
        new Chart(referralCtx, {
            type: 'doughnut',
            data: {
                labels: ['Level 1', 'Level 2', 'Level 3'],
                datasets: [{
                    data: [0, 0, 0],
                    backgroundColor: ['#00D4AA', '#39FF14', '#0D1421'],
                    borderColor: ['#00D4AA', '#39FF14', '#1A1A1A'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: '#FFFFFF'
                        }
                    }
                }
            }
        });
    }
}

// Clipboard functionality for referral links
function initializeClipboard() {
    const copyButtons = document.querySelectorAll('.copy-btn');
    
    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                // Select and copy text
                targetElement.select();
                targetElement.setSelectionRange(0, 99999); // For mobile devices
                
                try {
                    document.execCommand('copy');
                    showNotification('Copied to clipboard!', 'success');
                    
                    // Visual feedback
                    this.innerHTML = '<i class="fas fa-check"></i> Copied!';
                    this.style.background = '#39FF14';
                    
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-copy"></i> Copy';
                        this.style.background = '';
                    }, 2000);
                } catch (err) {
                    showNotification('Failed to copy', 'error');
                }
            }
        });
    });
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        min-width: 250px;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Form validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const inputs = form.querySelectorAll('input[required], select[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            showFieldError(input, 'This field is required');
            isValid = false;
        } else {
            clearFieldError(input);
        }
        
        // Email validation
        if (input.type === 'email' && !isValidEmail(input.value)) {
            showFieldError(input, 'Please enter a valid email address');
            isValid = false;
        }
        
        // USDT wallet validation
        if (input.name === 'usdt_wallet' && !isValidUSDTWallet(input.value)) {
            showFieldError(input, 'Please enter a valid USDT wallet address');
            isValid = false;
        }
    });
    
    return isValid;
}

function showFieldError(input, message) {
    clearFieldError(input);
    
    const error = document.createElement('div');
    error.className = 'field-error';
    error.textContent = message;
    error.style.cssText = `
        color: #ff4343;
        font-size: 0.9rem;
        margin-top: 0.25rem;
    `;
    
    input.parentNode.appendChild(error);
    input.style.borderColor = '#ff4343';
}

function clearFieldError(input) {
    const error = input.parentNode.querySelector('.field-error');
    if (error) error.remove();
    input.style.borderColor = '';
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidUSDTWallet(wallet) {
    // Basic USDT wallet validation (TRC20/ERC20)
    const trc20Regex = /^T[A-Za-z1-9]{33}$/;
    const erc20Regex = /^0x[a-fA-F0-9]{40}$/;
    return trc20Regex.test(wallet) || erc20Regex.test(wallet);
}

// Loading states
function showLoading(buttonId) {
    const button = document.getElementById(buttonId);
    if (button) {
        button.innerHTML = '<span class="loading"></span> Processing...';
        button.disabled = true;
    }
}

function hideLoading(buttonId, originalText) {
    const button = document.getElementById(buttonId);
    if (button) {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Auto-refresh dashboard data
function startAutoRefresh() {
    setInterval(() => {
        refreshDashboardData();
    }, 30000); // Refresh every 30 seconds
}

function refreshDashboardData() {
    fetch('/api/dashboard-data')
        .then(response => response.json())
        .then(data => {
            updateDashboardElements(data);
        })
        .catch(error => console.error('Auto-refresh error:', error));
}

function updateDashboardElements(data) {
    // Update balance
    const balanceElement = document.getElementById('user-balance');
    if (balanceElement && data.balance !== undefined) {
        balanceElement.textContent = data.balance.toFixed(2) + ' USDT';
    }
    
    // Update daily earnings
    const earningsElement = document.getElementById('daily-earnings');
    if (earningsElement && data.daily_earnings !== undefined) {
        earningsElement.textContent = data.daily_earnings.toFixed(2) + ' USDT';
    }
    
    // Update total earned
    const totalEarnedElement = document.getElementById('total-earned');
    if (totalEarnedElement && data.total_earned !== undefined) {
        totalEarnedElement.textContent = data.total_earned.toFixed(2) + ' USDT';
    }
}

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .notification {
        animation: slideIn 0.3s ease;
    }
`;
document.head.appendChild(style);
