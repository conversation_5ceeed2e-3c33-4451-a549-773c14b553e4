{% extends "base.html" %}

{% block title %}Login - XCapital{% endblock %}

{% block content %}
<div class="section" style="padding-top: 8rem;">
    <div class="container">
        <div style="max-width: 500px; margin: 0 auto;">
            <div class="card">
                <h2 style="text-align: center; margin-bottom: 2rem;">
                    <i class="fas fa-sign-in-alt"></i> Login to XCapital
                </h2>
                
                <form method="POST" id="loginForm">
                    <div class="form-group">
                        <label for="username" class="form-label">
                            <i class="fas fa-user"></i> Username
                        </label>
                        <input type="text" id="username" name="username" class="form-input" required 
                               placeholder="Enter your username">
                    </div>
                    
                    <div class="form-group">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock"></i> Password
                        </label>
                        <input type="password" id="password" name="password" class="form-input" required 
                               placeholder="Enter your password">
                    </div>
                    
                    <button type="submit" class="btn" style="width: 100%; margin-top: 1rem;" id="loginBtn">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </button>
                </form>
                
                <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid rgba(0, 212, 170, 0.2);">
                    <p style="opacity: 0.8;">Don't have an account?</p>
                    <a href="{{ url_for('register') }}" class="btn btn-secondary" style="margin-top: 1rem;">
                        <i class="fas fa-user-plus"></i> Create Account
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('loginForm').addEventListener('submit', function(e) {
    if (!validateForm('loginForm')) {
        e.preventDefault();
        return false;
    }
    
    showLoading('loginBtn');
    // Form will submit normally after validation
});
</script>
{% endblock %}
