import os
from datetime import datetime
from flask import render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash
from app import app, db
from models import User, VIPPackage, Investment, Withdrawal, Payment, Commission, DailyEarning, SystemSettings
from auth import admin_required, active_user_required
from payment_handler import PaymentProcessor, WithdrawalProcessor

# Initialize payment processors
payment_processor = PaymentProcessor()
withdrawal_processor = WithdrawalProcessor()

@app.route('/')
def index():
    """Landing page"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            if user.is_active:
                login_user(user)
                user.last_login = datetime.utcnow()
                db.session.commit()
                
                next_page = request.args.get('next')
                if next_page:
                    return redirect(next_page)
                
                if user.is_admin:
                    return redirect(url_for('admin_dashboard'))
                else:
                    return redirect(url_for('dashboard'))
            else:
                flash('Your account has been deactivated.', 'error')
        else:
            flash('Invalid username or password.', 'error')
    
    return render_template('login.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    """User registration"""
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        usdt_wallet = request.form.get('usdt_wallet')
        referral_code = request.form.get('referral_code')
        
        # Validation
        if User.query.filter_by(username=username).first():
            flash('Username already exists.', 'error')
            return render_template('register.html')
        
        if User.query.filter_by(email=email).first():
            flash('Email already registered.', 'error')
            return render_template('register.html')
        
        # Find referrer
        referrer = None
        if referral_code:
            referrer = User.query.filter_by(referral_code=referral_code).first()
            if not referrer:
                flash('Invalid referral code.', 'error')
                return render_template('register.html')
        
        # Create user
        user = User(
            username=username,
            email=email,
            usdt_wallet=usdt_wallet,
            referred_by=referrer.id if referrer else None
        )
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        flash('Registration successful! Please login.', 'success')
        return redirect(url_for('login'))
    
    return render_template('register.html')

@app.route('/logout')
@login_required
def logout():
    """User logout"""
    logout_user()
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
@active_user_required
def dashboard():
    """User dashboard"""
    # Get user statistics
    investments = Investment.query.filter_by(user_id=current_user.id).all()
    active_investments = [inv for inv in investments if inv.is_active and inv.expires_at > datetime.utcnow()]
    
    recent_earnings = DailyEarning.query.filter_by(user_id=current_user.id)\
        .order_by(DailyEarning.created_at.desc()).limit(10).all()
    
    referral_stats = current_user.get_referral_stats()
    total_commissions = sum([c.amount for c in current_user.commissions])
    
    return render_template('dashboard.html',
                         user=current_user,
                         investments=investments,
                         active_investments=active_investments,
                         recent_earnings=recent_earnings,
                         referral_stats=referral_stats,
                         total_commissions=total_commissions)

@app.route('/packages')
@login_required
@active_user_required
def packages():
    """VIP packages page"""
    vip_packages = VIPPackage.query.filter_by(is_active=True).order_by(VIPPackage.id).all()
    return render_template('packages.html', packages=vip_packages)

@app.route('/purchase/<int:package_id>')
@login_required
@active_user_required
def purchase_package(package_id):
    """Purchase VIP package"""
    package = VIPPackage.query.get_or_404(package_id)
    
    # Create payment
    payment_info = payment_processor.create_payment(current_user.id, package.id, package.price)
    
    if payment_info:
        return redirect(payment_info['pay_url'])
    else:
        flash('Payment creation failed. Please try again.', 'error')
        return redirect(url_for('packages'))

@app.route('/referrals')
@login_required
@active_user_required
def referrals():
    """Referrals page"""
    # Get direct referrals
    direct_referrals = User.query.filter_by(referred_by=current_user.id).all()
    
    # Get referral statistics
    referral_stats = current_user.get_referral_stats()
    
    # Get commission history
    commissions = Commission.query.filter_by(user_id=current_user.id)\
        .order_by(Commission.created_at.desc()).limit(20).all()
    
    referral_link = f"{request.host_url}register?ref={current_user.referral_code}"
    
    return render_template('referrals.html',
                         direct_referrals=direct_referrals,
                         referral_stats=referral_stats,
                         commissions=commissions,
                         referral_link=referral_link)

@app.route('/withdraw', methods=['GET', 'POST'])
@login_required
@active_user_required
def withdraw():
    """Withdrawal page"""
    if request.method == 'POST':
        amount = float(request.form.get('amount', 0))
        wallet_address = request.form.get('wallet_address')
        
        # Validation
        if amount <= 0:
            flash('Invalid withdrawal amount.', 'error')
        elif amount > current_user.balance:
            flash('Insufficient balance.', 'error')
        elif not wallet_address:
            flash('Wallet address is required.', 'error')
        else:
            # Create withdrawal request
            withdrawal = Withdrawal(
                user_id=current_user.id,
                amount=amount,
                wallet_address=wallet_address
            )
            
            # Deduct from user balance
            current_user.balance -= amount
            
            db.session.add(withdrawal)
            db.session.commit()
            
            # Process withdrawal automatically
            withdrawal_processor.process_withdrawal(withdrawal.id)
            
            flash('Withdrawal request submitted successfully!', 'success')
            return redirect(url_for('withdraw'))
    
    # Get withdrawal history
    withdrawals = Withdrawal.query.filter_by(user_id=current_user.id)\
        .order_by(Withdrawal.created_at.desc()).limit(10).all()
    
    return render_template('withdraw.html', withdrawals=withdrawals)

@app.route('/admin')
@login_required
@admin_required
def admin_dashboard():
    """Admin dashboard"""
    # System statistics
    total_users = User.query.count()
    active_users = User.query.filter_by(is_active=True).count()
    total_investments = Investment.query.count()
    total_invested = db.session.query(db.func.sum(Investment.amount)).scalar() or 0
    total_withdrawn = db.session.query(db.func.sum(Withdrawal.amount)).filter_by(status='completed').scalar() or 0
    pending_withdrawals = Withdrawal.query.filter_by(status='pending').count()
    
    # Recent activities
    recent_users = User.query.order_by(User.created_at.desc()).limit(10).all()
    recent_investments = Investment.query.order_by(Investment.created_at.desc()).limit(10).all()
    recent_withdrawals = Withdrawal.query.order_by(Withdrawal.created_at.desc()).limit(10).all()
    
    return render_template('admin.html',
                         total_users=total_users,
                         active_users=active_users,
                         total_investments=total_investments,
                         total_invested=total_invested,
                         total_withdrawn=total_withdrawn,
                         pending_withdrawals=pending_withdrawals,
                         recent_users=recent_users,
                         recent_investments=recent_investments,
                         recent_withdrawals=recent_withdrawals)

@app.route('/webhook/payment', methods=['POST'])
def payment_webhook():
    """NowPayments.io webhook handler"""
    try:
        data = request.get_json()
        payment_id = data.get('payment_id')
        payment_status = data.get('payment_status')
        
        if payment_status == 'finished':
            payment_processor.process_confirmed_payment(payment_id)
        
        return jsonify({'status': 'ok'})
    except Exception as e:
        app.logger.error(f"Webhook error: {str(e)}")
        return jsonify({'error': 'webhook_error'}), 400

@app.route('/payment/success')
@login_required
def payment_success():
    """Payment success page"""
    flash('Payment submitted successfully! Your investment will be activated once confirmed.', 'success')
    return redirect(url_for('dashboard'))

@app.route('/payment/cancel')
@login_required
def payment_cancel():
    """Payment cancel page"""
    flash('Payment was cancelled.', 'info')
    return redirect(url_for('packages'))

@app.route('/webhook/cryptomus/payment', methods=['POST'])
def cryptomus_payment_webhook():
    """Cryptomus payment webhook handler"""
    try:
        from cryptomus_handler import cryptomus_processor

        data = request.get_json()
        signature = request.headers.get('sign')

        # Verify webhook signature
        if not cryptomus_processor.verify_webhook_signature(data, signature):
            app.logger.error("Invalid Cryptomus webhook signature")
            return jsonify({'error': 'invalid_signature'}), 400

        payment_status = data.get('status')
        order_id = data.get('order_id')

        if payment_status == 'paid' and order_id:
            # Extract user_id and package_id from order_id
            # Format: XCAPITAL_USER_{user_id}_PKG_{package_id}_{timestamp}
            try:
                parts = order_id.split('_')
                user_id = int(parts[2])
                package_id = int(parts[4])

                # Process the confirmed payment
                payment_processor.process_confirmed_payment_cryptomus(
                    user_id, package_id, data
                )

            except (IndexError, ValueError) as e:
                app.logger.error(f"Invalid order_id format: {order_id}")
                return jsonify({'error': 'invalid_order_id'}), 400

        return jsonify({'status': 'ok'})

    except Exception as e:
        app.logger.error(f"Cryptomus payment webhook error: {str(e)}")
        return jsonify({'error': 'webhook_error'}), 400

@app.route('/webhook/cryptomus/payout', methods=['POST'])
def cryptomus_payout_webhook():
    """Cryptomus payout webhook handler for withdrawal confirmations"""
    try:
        from cryptomus_handler import cryptomus_processor

        data = request.get_json()
        signature = request.headers.get('sign')

        # Verify webhook signature
        if not cryptomus_processor.verify_webhook_signature(data, signature):
            app.logger.error("Invalid Cryptomus payout webhook signature")
            return jsonify({'error': 'invalid_signature'}), 400

        payout_status = data.get('status')
        order_id = data.get('order_id')
        payout_uuid = data.get('uuid')

        if order_id:
            # Extract withdrawal_id from order_id
            # Format: XCAPITAL_WITHDRAWAL_{withdrawal_id}_{timestamp}
            try:
                parts = order_id.split('_')
                withdrawal_id = int(parts[2])

                # Update withdrawal status based on payout status
                withdrawal = Withdrawal.query.get(withdrawal_id)
                if withdrawal:
                    if payout_status == 'paid':
                        withdrawal.status = 'completed'
                        withdrawal.processed_at = datetime.utcnow()
                        withdrawal.transaction_id = data.get('txid', payout_uuid)

                        # Update user balance
                        user = User.query.get(withdrawal.user_id)
                        user.total_withdrawn += withdrawal.amount

                        app.logger.info(f"Cryptomus withdrawal {withdrawal_id} completed")

                    elif payout_status == 'fail':
                        withdrawal.status = 'failed'
                        withdrawal.notes = f"Cryptomus payout failed: {data.get('message', 'Unknown error')}"
                        app.logger.error(f"Cryptomus withdrawal {withdrawal_id} failed")

                    db.session.commit()

            except (IndexError, ValueError) as e:
                app.logger.error(f"Invalid payout order_id format: {order_id}")
                return jsonify({'error': 'invalid_order_id'}), 400

        return jsonify({'status': 'ok'})

    except Exception as e:
        app.logger.error(f"Cryptomus payout webhook error: {str(e)}")
        return jsonify({'error': 'webhook_error'}), 400

# Initialize VIP packages if they don't exist
def initialize_data():
    """Initialize default data"""
    with app.app_context():
        if VIPPackage.query.count() == 0:
            packages = [
            {'name': 'VIP0', 'price': 0.0, 'daily_return': 0.10, 'duration_days': 5},
            {'name': 'VIP1', 'price': 18.0, 'daily_return': 3.50, 'duration_days': 90},
            {'name': 'VIP2', 'price': 80.0, 'daily_return': 16.0, 'duration_days': 90},
            {'name': 'VIP3', 'price': 200.0, 'daily_return': 41.0, 'duration_days': 90},
            {'name': 'VIP4', 'price': 500.0, 'daily_return': 106.0, 'duration_days': 90},
            {'name': 'VIP5', 'price': 1200.0, 'daily_return': 260.0, 'duration_days': 90},
            {'name': 'VIP6', 'price': 2500.0, 'daily_return': 555.0, 'duration_days': 90},
            {'name': 'VIP7', 'price': 5000.0, 'daily_return': 1162.0, 'duration_days': 90},
            {'name': 'VIP8', 'price': 12000.0, 'daily_return': 2926.0, 'duration_days': 90},
            {'name': 'VIP9', 'price': 30000.0, 'daily_return': 8570.0, 'duration_days': 90},
            {'name': 'VIP10', 'price': 60000.0, 'daily_return': 20000.0, 'duration_days': 90},
        ]
        
        for pkg_data in packages:
            package = VIPPackage(**pkg_data)
            db.session.add(package)
        
        db.session.commit()
    
        # Create admin user if doesn't exist
        if not User.query.filter_by(is_admin=True).first():
            admin = User(
            username='admin',
            email='<EMAIL>',
            is_admin=True
        )
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
