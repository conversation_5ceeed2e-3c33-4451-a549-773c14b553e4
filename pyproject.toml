[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    "email-validator>=2.2.0",
    "flask-dance>=7.1.0",
    "flask>=3.1.1",
    "flask-sqlalchemy>=3.1.1",
    "gunicorn>=23.0.0",
    "psycopg2-binary>=2.9.10",
    "flask-login>=0.6.3",
    "oauthlib>=3.3.1",
    "pyjwt>=2.10.1",
    "requests>=2.32.4",
    "sqlalchemy>=2.0.41",
    "werkzeug>=3.1.3",
    "apscheduler>=3.11.0",
    "mysql-connector-python>=9.3.0",
    "pymysql>=1.1.1",
    "python-dotenv>=1.0.0",
]
