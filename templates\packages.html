{% extends "base.html" %}

{% block title %}VIP Packages - XCapital{% endblock %}

{% block content %}
<div class="section" style="padding-top: 8rem;">
    <div class="container">
        <!-- Header -->
        <div class="card" style="text-align: center; background: var(--gradient-dark); margin-bottom: 3rem;">
            <h1 style="margin-bottom: 1rem;">
                <i class="fas fa-gem"></i> VIP Investment Packages
            </h1>
            <p style="font-size: 1.2rem; opacity: 0.9; margin-bottom: 2rem;">
                Choose from 11 exclusive VIP levels with guaranteed daily returns for 90 days
            </p>
            
            <div class="stats-grid" style="margin-top: 2rem;">
                <div class="stat-card">
                    <div class="stat-value">90</div>
                    <div class="stat-label">Days Duration</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">24/7</div>
                    <div class="stat-label">Withdrawals</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">365</div>
                    <div class="stat-label">Days Per Year</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">08:00</div>
                    <div class="stat-label">Daily Updates (EST)</div>
                </div>
            </div>
        </div>

        <!-- Package Grid -->
        <div class="packages-grid">
            {% for package in packages %}
            <div class="vip-card {% if package.name == 'VIP5' %}glow-accent{% endif %}">
                <div class="vip-card-content">
                    <!-- Package Level -->
                    <div class="vip-level">
                        {{ package.name }}
                        {% if package.name == 'VIP0' %}
                            <span style="font-size: 1rem; color: var(--accent-green); display: block; margin-top: 0.5rem;">
                                <i class="fas fa-gift"></i> FREE TRIAL
                            </span>
                        {% elif package.name == 'VIP5' %}
                            <span style="font-size: 1rem; color: var(--accent-green); display: block; margin-top: 0.5rem;">
                                <i class="fas fa-star"></i> MOST POPULAR
                            </span>
                        {% elif package.name == 'VIP10' %}
                            <span style="font-size: 1rem; color: var(--accent-green); display: block; margin-top: 0.5rem;">
                                <i class="fas fa-crown"></i> MAXIMUM RETURNS
                            </span>
                        {% endif %}
                    </div>
                    
                    <!-- Package Price -->
                    <div class="vip-price">
                        {% if package.price == 0 %}
                            FREE
                        {% else %}
                            ${{ "%.0f"|format(package.price) }} USDT
                        {% endif %}
                    </div>
                    
                    <!-- Daily Return -->
                    <div class="vip-daily">
                        ${{ "%.2f"|format(package.daily_return) }} USDT Daily
                    </div>
                    
                    <!-- Package Details -->
                    <div style="margin: 2rem 0; padding: 1rem; background: rgba(0, 0, 0, 0.3); border-radius: 10px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 1rem;">
                            <span style="opacity: 0.8;">Duration:</span>
                            <span style="color: var(--primary-green);">{{ package.duration_days }} days</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 1rem;">
                            <span style="opacity: 0.8;">Daily Yield:</span>
                            <span style="color: var(--accent-green);">
                                {% if package.price > 0 %}
                                    {{ "%.2f"|format((package.daily_return / package.price) * 100) }}%
                                {% else %}
                                    N/A
                                {% endif %}
                            </span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 1rem;">
                            <span style="opacity: 0.8;">Total Return:</span>
                            <span style="color: var(--primary-green);">
                                ${{ "%.2f"|format(package.daily_return * package.duration_days) }} USDT
                            </span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span style="opacity: 0.8;">Net Profit:</span>
                            <span style="color: var(--accent-green);">
                                ${{ "%.2f"|format((package.daily_return * package.duration_days) - package.price) }} USDT
                            </span>
                        </div>
                    </div>
                    
                    <!-- Features -->
                    <div style="margin-bottom: 2rem;">
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin-bottom: 0.5rem;">
                                <i class="fas fa-check" style="color: var(--primary-green); margin-right: 0.5rem;"></i>
                                Daily automatic payments
                            </li>
                            <li style="margin-bottom: 0.5rem;">
                                <i class="fas fa-check" style="color: var(--primary-green); margin-right: 0.5rem;"></i>
                                Instant withdrawal anytime
                            </li>
                            <li style="margin-bottom: 0.5rem;">
                                <i class="fas fa-check" style="color: var(--primary-green); margin-right: 0.5rem;"></i>
                                Multiple packages allowed
                            </li>
                            <li style="margin-bottom: 0.5rem;">
                                <i class="fas fa-check" style="color: var(--primary-green); margin-right: 0.5rem;"></i>
                                Referral commissions
                            </li>
                        </ul>
                    </div>
                    
                    <!-- Purchase Button -->
                    {% if current_user.is_authenticated %}
                        {% if package.price == 0 %}
                            <button class="btn" onclick="claimFreePackage({{ package.id }})" style="width: 100%;">
                                <i class="fas fa-gift"></i> Claim Free Package
                            </button>
                        {% else %}
                            <a href="{{ url_for('purchase_package', package_id=package.id) }}" class="btn" style="width: 100%; text-align: center; display: block;">
                                <i class="fas fa-unlock"></i> Purchase Now
                            </a>
                        {% endif %}
                    {% else %}
                        <a href="{{ url_for('register') }}" class="btn" style="width: 100%; text-align: center; display: block;">
                            <i class="fas fa-user-plus"></i> Register to Purchase
                        </a>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Package Comparison -->
        <div class="card" style="margin-top: 4rem;">
            <h3 style="text-align: center; margin-bottom: 2rem;">
                <i class="fas fa-chart-bar"></i> Package Comparison
            </h3>
            
            <div class="table" style="overflow-x: auto;">
                <table style="width: 100%; min-width: 800px;">
                    <thead>
                        <tr>
                            <th><i class="fas fa-gem"></i> Package</th>
                            <th><i class="fas fa-dollar-sign"></i> Investment</th>
                            <th><i class="fas fa-calendar-day"></i> Daily Return</th>
                            <th><i class="fas fa-percentage"></i> Daily Yield</th>
                            <th><i class="fas fa-coins"></i> Total Return</th>
                            <th><i class="fas fa-chart-line"></i> Net Profit</th>
                            <th><i class="fas fa-clock"></i> ROI Time</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for package in packages %}
                        <tr>
                            <td>
                                <span style="color: var(--primary-green); font-weight: bold;">
                                    {{ package.name }}
                                </span>
                            </td>
                            <td>
                                {% if package.price == 0 %}
                                    <span style="color: var(--accent-green);">FREE</span>
                                {% else %}
                                    ${{ "%.0f"|format(package.price) }}
                                {% endif %}
                            </td>
                            <td>
                                <span style="color: var(--accent-green);">
                                    ${{ "%.2f"|format(package.daily_return) }}
                                </span>
                            </td>
                            <td>
                                {% if package.price > 0 %}
                                    {{ "%.2f"|format((package.daily_return / package.price) * 100) }}%
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td>
                                ${{ "%.2f"|format(package.daily_return * package.duration_days) }}
                            </td>
                            <td>
                                <span style="color: var(--primary-green);">
                                    ${{ "%.2f"|format((package.daily_return * package.duration_days) - package.price) }}
                                </span>
                            </td>
                            <td>
                                {% if package.price > 0 %}
                                    {{ "%.0f"|format(package.price / package.daily_return) }} days
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Package Benefits -->
        <div class="card" style="background: var(--gradient-dark); margin-top: 3rem;">
            <h3 style="text-align: center; margin-bottom: 2rem;">
                <i class="fas fa-star"></i> Why Choose Our VIP Packages?
            </h3>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                <div style="text-align: center; padding: 1rem;">
                    <div style="font-size: 3rem; color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h4 style="color: var(--accent-green); margin-bottom: 1rem;">Fully Automated</h4>
                    <p style="opacity: 0.9;">
                        Our advanced algorithms handle all investment processes automatically, 
                        ensuring consistent daily returns without any manual intervention.
                    </p>
                </div>
                
                <div style="text-align: center; padding: 1rem;">
                    <div style="font-size: 3rem; color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4 style="color: var(--accent-green); margin-bottom: 1rem;">Guaranteed Returns</h4>
                    <p style="opacity: 0.9;">
                        Every VIP package comes with guaranteed daily returns for the full 90-day period, 
                        providing predictable and stable income.
                    </p>
                </div>
                
                <div style="text-align: center; padding: 1rem;">
                    <div style="font-size: 3rem; color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <h4 style="color: var(--accent-green); margin-bottom: 1rem;">Multiple Packages</h4>
                    <p style="opacity: 0.9;">
                        You can purchase and combine multiple VIP packages simultaneously 
                        to maximize your daily earnings potential.
                    </p>
                </div>
            </div>
        </div>

        <!-- Free Package Rewards -->
        <div class="card" style="margin-top: 3rem;">
            <h3 style="text-align: center; margin-bottom: 2rem;">
                <i class="fas fa-gift"></i> Free VIP Package Rewards
            </h3>
            
            <p style="text-align: center; font-size: 1.1rem; margin-bottom: 2rem; opacity: 0.9;">
                Invite users to upgrade to VIP packages and earn free packages as rewards!
            </p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div class="stat-card">
                    <div class="stat-value">10</div>
                    <div class="stat-label">VIP1 Referrals</div>
                    <small style="color: var(--accent-green);">= Free VIP1 Package</small>
                </div>
                <div class="stat-card">
                    <div class="stat-value">10</div>
                    <div class="stat-label">VIP2 Referrals</div>
                    <small style="color: var(--accent-green);">= Free VIP2 Package</small>
                </div>
                <div class="stat-card">
                    <div class="stat-value">10</div>
                    <div class="stat-label">VIP3 Referrals</div>
                    <small style="color: var(--accent-green);">= Free VIP3 Package</small>
                </div>
                <div class="stat-card">
                    <div class="stat-value">10</div>
                    <div class="stat-label">VIP4 Referrals</div>
                    <small style="color: var(--accent-green);">= Free VIP4 Package</small>
                </div>
                <div class="stat-card">
                    <div class="stat-value">8</div>
                    <div class="stat-label">VIP5 Referrals</div>
                    <small style="color: var(--accent-green);">= Free VIP5 Package</small>
                </div>
                <div class="stat-card">
                    <div class="stat-value">7</div>
                    <div class="stat-label">VIP6 Referrals</div>
                    <small style="color: var(--accent-green);">= Free VIP6 Package</small>
                </div>
                <div class="stat-card">
                    <div class="stat-value">7</div>
                    <div class="stat-label">VIP7 Referrals</div>
                    <small style="color: var(--accent-green);">= Free VIP7 Package</small>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        {% if not current_user.is_authenticated %}
        <div class="card" style="text-align: center; background: var(--gradient-dark); margin-top: 3rem;">
            <h3 style="margin-bottom: 2rem;">Ready to Start Earning?</h3>
            <p style="font-size: 1.1rem; margin-bottom: 2rem; opacity: 0.9;">
                Join thousands of satisfied investors who are already earning daily profits with XCapital
            </p>
            
            <div style="display: flex; gap: 2rem; justify-content: center; flex-wrap: wrap;">
                <a href="{{ url_for('register') }}" class="btn" style="font-size: 1.1rem; padding: 1rem 2rem;">
                    <i class="fas fa-user-plus"></i> Register Now
                </a>
                <a href="{{ url_for('login') }}" class="btn btn-secondary" style="font-size: 1.1rem; padding: 1rem 2rem;">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function claimFreePackage(packageId) {
    if (confirm('Are you sure you want to claim the free VIP0 package?')) {
        showLoading('claimBtn');
        
        fetch('/claim-free-package', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                package_id: packageId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Free package claimed successfully!', 'success');
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 2000);
            } else {
                showNotification('Failed to claim free package: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Error claiming free package', 'error');
        });
    }
}

// Add hover effects for package cards
document.querySelectorAll('.vip-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-10px) scale(1.02)';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});
</script>
{% endblock %}
