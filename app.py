import os
import logging
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from sqlalchemy.orm import DeclarativeBase
from werkzeug.middleware.proxy_fix import ProxyFix
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.DEBUG)

class Base(DeclarativeBase):
    pass

# Initialize Flask app
app = Flask(__name__)
app.secret_key = os.environ.get("SESSION_SECRET")
app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)

# Database configuration with fallback
database_url = os.environ.get("DATABASE_URL")

# Try MySQL first, fallback to SQLite if connection fails
try:
    if database_url and database_url.startswith('mysql'):
        # Test MySQL connection
        from sqlalchemy import create_engine
        test_engine = create_engine(database_url)
        test_engine.connect().close()
        app.config["SQLALCHEMY_DATABASE_URI"] = database_url
        logging.info("Using MySQL database")
    else:
        raise Exception("MySQL not configured")
except Exception as e:
    # Fallback to SQLite
    sqlite_url = "sqlite:///xcapital_local.db"
    app.config["SQLALCHEMY_DATABASE_URI"] = sqlite_url
    logging.warning(f"MySQL connection failed: {e}")
    logging.info("Using SQLite database as fallback")

app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False
app.config["SQLALCHEMY_ENGINE_OPTIONS"] = {
    'pool_pre_ping': True,
    "pool_recycle": 300,
}

# Initialize extensions
db = SQLAlchemy(app, model_class=Base)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page.'

# User loader for Flask-Login
@login_manager.user_loader
def load_user(user_id):
    from models import User
    return User.query.get(int(user_id))

# Create tables
with app.app_context():
    try:
        import models  # noqa: F401
        db.create_all()
        logging.info("Database tables created successfully")
    except Exception as e:
        logging.error(f"Error creating database tables: {e}")
        raise
