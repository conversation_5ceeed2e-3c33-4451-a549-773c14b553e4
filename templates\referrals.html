{% extends "base.html" %}

{% block title %}Referrals - XCapital{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="section" style="padding-top: 8rem;">
    <div class="container">
        <!-- Header -->
        <div class="card" style="text-align: center; background: var(--gradient-dark); margin-bottom: 3rem;">
            <h1 style="margin-bottom: 1rem;">
                <i class="fas fa-users"></i> Referral Program
            </h1>
            <p style="font-size: 1.2rem; opacity: 0.9; margin-bottom: 2rem;">
                Build your network and earn lifetime commissions from 3 levels of referrals
            </p>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">10%</div>
                    <div class="stat-label">Level 1 Commission</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">5%</div>
                    <div class="stat-label">Level 2 Commission</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">1%</div>
                    <div class="stat-label">Level 3 Commission</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">∞</div>
                    <div class="stat-label">Lifetime Duration</div>
                </div>
            </div>
        </div>

        <!-- Your Referral Stats -->
        <div class="stats-grid">
            <div class="stat-card glow">
                <div class="stat-value">{{ referral_stats.level1 }}</div>
                <div class="stat-label">
                    <i class="fas fa-user"></i> Level 1 Referrals
                </div>
                <small style="color: var(--primary-green);">Direct referrals (10%)</small>
            </div>
            
            <div class="stat-card glow">
                <div class="stat-value">{{ referral_stats.level2 }}</div>
                <div class="stat-label">
                    <i class="fas fa-sitemap"></i> Level 2 Referrals
                </div>
                <small style="color: var(--primary-green);">Second tier (5%)</small>
            </div>
            
            <div class="stat-card glow">
                <div class="stat-value">{{ referral_stats.level3 }}</div>
                <div class="stat-label">
                    <i class="fas fa-network-wired"></i> Level 3 Referrals
                </div>
                <small style="color: var(--primary-green);">Third tier (1%)</small>
            </div>
            
            <div class="stat-card glow-accent">
                <div class="stat-value">${{ "%.2f"|format(commissions|sum(attribute='amount') or 0) }}</div>
                <div class="stat-label">
                    <i class="fas fa-coins"></i> Total Commissions
                </div>
                <small style="color: var(--accent-green);">Lifetime earnings (USDT)</small>
            </div>
        </div>

        <!-- Your Referral Link -->
        <div class="card" style="background: var(--gradient-dark);">
            <h3 style="text-align: center; margin-bottom: 2rem;">
                <i class="fas fa-link"></i> Your Referral Link
            </h3>
            
            <div style="margin-bottom: 2rem;">
                <label class="form-label" style="margin-bottom: 0.5rem;">
                    <i class="fas fa-share-alt"></i> Share this link to earn commissions
                </label>
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <input type="text" 
                           value="{{ referral_link }}" 
                           id="referralLink" 
                           class="form-input" 
                           readonly
                           style="flex: 1;">
                    <button class="btn copy-btn" data-target="referralLink">
                        <i class="fas fa-copy"></i> Copy Link
                    </button>
                </div>
            </div>
            
            <div style="margin-bottom: 2rem;">
                <label class="form-label" style="margin-bottom: 0.5rem;">
                    <i class="fas fa-code"></i> Your Referral Code
                </label>
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <input type="text" 
                           value="{{ current_user.referral_code }}" 
                           id="referralCode" 
                           class="form-input" 
                           readonly
                           style="flex: 1;">
                    <button class="btn copy-btn" data-target="referralCode">
                        <i class="fas fa-copy"></i> Copy Code
                    </button>
                </div>
            </div>
            
            <div style="text-align: center;">
                <p style="opacity: 0.9; margin-bottom: 1rem;">
                    Share your referral link and earn commissions every time someone invests!
                </p>
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <button class="btn btn-secondary" onclick="shareOnWhatsApp()">
                        <i class="fab fa-whatsapp"></i> WhatsApp
                    </button>
                    <button class="btn btn-secondary" onclick="shareOnTelegram()">
                        <i class="fab fa-telegram"></i> Telegram
                    </button>
                    <button class="btn btn-secondary" onclick="shareOnTwitter()">
                        <i class="fab fa-twitter"></i> Twitter
                    </button>
                    <button class="btn btn-secondary" onclick="shareOnFacebook()">
                        <i class="fab fa-facebook"></i> Facebook
                    </button>
                </div>
            </div>
        </div>

        <!-- Commission Structure -->
        <div class="card">
            <h3 style="text-align: center; margin-bottom: 2rem;">
                <i class="fas fa-chart-pyramid"></i> 3-Level Commission Structure
            </h3>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                <div style="text-align: center; padding: 2rem; background: rgba(0, 212, 170, 0.1); border-radius: 15px; border: 2px solid var(--primary-green);">
                    <div style="font-size: 4rem; color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4 style="color: var(--primary-green); margin-bottom: 1rem;">Level 1 - Direct</h4>
                    <div style="font-size: 2rem; color: var(--accent-green); margin-bottom: 1rem;">10%</div>
                    <p style="opacity: 0.9; margin-bottom: 1rem;">
                        Earn 10% commission from every investment made by users you directly refer
                    </p>
                    <div style="background: rgba(0, 0, 0, 0.3); padding: 1rem; border-radius: 8px;">
                        <strong>Example:</strong><br>
                        Your referral invests $1,000<br>
                        <span style="color: var(--accent-green);">You earn: $100 instantly!</span>
                    </div>
                </div>
                
                <div style="text-align: center; padding: 2rem; background: rgba(0, 212, 170, 0.05); border-radius: 15px; border: 2px solid rgba(0, 212, 170, 0.3);">
                    <div style="font-size: 4rem; color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <h4 style="color: var(--primary-green); margin-bottom: 1rem;">Level 2 - Second Tier</h4>
                    <div style="font-size: 2rem; color: var(--accent-green); margin-bottom: 1rem;">5%</div>
                    <p style="opacity: 0.9; margin-bottom: 1rem;">
                        Receive 5% commission from investments made by referrals of your referrals
                    </p>
                    <div style="background: rgba(0, 0, 0, 0.3); padding: 1rem; border-radius: 8px;">
                        <strong>Example:</strong><br>
                        Level 2 user invests $1,000<br>
                        <span style="color: var(--accent-green);">You earn: $50 instantly!</span>
                    </div>
                </div>
                
                <div style="text-align: center; padding: 2rem; background: rgba(0, 212, 170, 0.02); border-radius: 15px; border: 2px solid rgba(0, 212, 170, 0.1);">
                    <div style="font-size: 4rem; color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-network-wired"></i>
                    </div>
                    <h4 style="color: var(--primary-green); margin-bottom: 1rem;">Level 3 - Third Tier</h4>
                    <div style="font-size: 2rem; color: var(--accent-green); margin-bottom: 1rem;">1%</div>
                    <p style="opacity: 0.9; margin-bottom: 1rem;">
                        Get 1% commission from the third level of your referral network
                    </p>
                    <div style="background: rgba(0, 0, 0, 0.3); padding: 1rem; border-radius: 8px;">
                        <strong>Example:</strong><br>
                        Level 3 user invests $1,000<br>
                        <span style="color: var(--accent-green);">You earn: $10 instantly!</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Direct Referrals -->
        <div class="card">
            <h3 style="margin-bottom: 2rem;">
                <i class="fas fa-user-friends"></i> Your Direct Referrals (Level 1)
            </h3>
            
            {% if direct_referrals %}
                <div class="table">
                    <table style="width: 100%;">
                        <thead>
                            <tr>
                                <th><i class="fas fa-user"></i> Username</th>
                                <th><i class="fas fa-envelope"></i> Email</th>
                                <th><i class="fas fa-calendar"></i> Joined</th>
                                <th><i class="fas fa-gem"></i> Investments</th>
                                <th><i class="fas fa-dollar-sign"></i> Total Invested</th>
                                <th><i class="fas fa-coins"></i> Your Commissions</th>
                                <th><i class="fas fa-signal"></i> Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for referral in direct_referrals %}
                            <tr>
                                <td>
                                    <span style="color: var(--primary-green); font-weight: bold;">
                                        {{ referral.username }}
                                    </span>
                                </td>
                                <td>{{ referral.email }}</td>
                                <td>{{ referral.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>{{ referral.investments|length }}</td>
                                <td>${{ "%.2f"|format(referral.total_invested) }} USDT</td>
                                <td>
                                    <span style="color: var(--accent-green);">
                                        ${{ "%.2f"|format(referral.total_invested * 0.10) }} USDT
                                    </span>
                                </td>
                                <td>
                                    {% if referral.is_active %}
                                        <span style="color: var(--accent-green);">
                                            <i class="fas fa-check-circle"></i> Active
                                        </span>
                                    {% else %}
                                        <span style="color: #ff4343;">
                                            <i class="fas fa-times-circle"></i> Inactive
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div style="text-align: center; padding: 3rem; opacity: 0.7;">
                    <i class="fas fa-user-plus" style="font-size: 4rem; color: var(--primary-green); margin-bottom: 1rem;"></i>
                    <h4>No Direct Referrals Yet</h4>
                    <p style="margin: 1rem 0;">Start sharing your referral link to build your network</p>
                    <button class="btn copy-btn" data-target="referralLink">
                        <i class="fas fa-copy"></i> Copy Referral Link
                    </button>
                </div>
            {% endif %}
        </div>

        <!-- Commission History -->
        <div class="card">
            <h3 style="margin-bottom: 2rem;">
                <i class="fas fa-history"></i> Commission History
            </h3>
            
            {% if commissions %}
                <div class="table">
                    <table style="width: 100%;">
                        <thead>
                            <tr>
                                <th><i class="fas fa-calendar"></i> Date</th>
                                <th><i class="fas fa-user"></i> From User</th>
                                <th><i class="fas fa-layer-group"></i> Level</th>
                                <th><i class="fas fa-percentage"></i> Rate</th>
                                <th><i class="fas fa-gem"></i> Investment</th>
                                <th><i class="fas fa-dollar-sign"></i> Commission</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for commission in commissions %}
                            <tr>
                                <td>{{ commission.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <span style="color: var(--primary-green);">
                                        {{ commission.from_user.username }}
                                    </span>
                                </td>
                                <td>
                                    <span style="color: var(--accent-green);">
                                        Level {{ commission.level }}
                                    </span>
                                </td>
                                <td>{{ "%.1f"|format(commission.rate * 100) }}%</td>
                                <td>
                                    <span style="color: var(--primary-green);">
                                        {{ commission.investment.package.name }}
                                    </span>
                                </td>
                                <td>
                                    <span style="color: var(--accent-green);">
                                        +${{ "%.2f"|format(commission.amount) }} USDT
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div style="text-align: center; padding: 3rem; opacity: 0.7;">
                    <i class="fas fa-coins" style="font-size: 4rem; color: var(--primary-green); margin-bottom: 1rem;"></i>
                    <h4>No Commissions Yet</h4>
                    <p>Your commission history will appear here once your referrals start investing</p>
                </div>
            {% endif %}
        </div>

        <!-- Free Package Rewards -->
        <div class="card" style="background: var(--gradient-dark);">
            <h3 style="text-align: center; margin-bottom: 2rem;">
                <i class="fas fa-gift"></i> Free Package Rewards
            </h3>
            
            <p style="text-align: center; font-size: 1.1rem; margin-bottom: 2rem; opacity: 0.9;">
                Invite users to upgrade to specific VIP packages and earn free packages as rewards!
            </p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                <div class="stat-card">
                    <div class="stat-value">10</div>
                    <div class="stat-label">VIP1 Invites</div>
                    <div style="color: var(--accent-green); margin-top: 0.5rem;">
                        <i class="fas fa-arrow-right"></i> Free VIP1 Package
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value">10</div>
                    <div class="stat-label">VIP2 Invites</div>
                    <div style="color: var(--accent-green); margin-top: 0.5rem;">
                        <i class="fas fa-arrow-right"></i> Free VIP2 Package
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value">10</div>
                    <div class="stat-label">VIP3 Invites</div>
                    <div style="color: var(--accent-green); margin-top: 0.5rem;">
                        <i class="fas fa-arrow-right"></i> Free VIP3 Package
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value">10</div>
                    <div class="stat-label">VIP4 Invites</div>
                    <div style="color: var(--accent-green); margin-top: 0.5rem;">
                        <i class="fas fa-arrow-right"></i> Free VIP4 Package
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value">8</div>
                    <div class="stat-label">VIP5 Invites</div>
                    <div style="color: var(--accent-green); margin-top: 0.5rem;">
                        <i class="fas fa-arrow-right"></i> Free VIP5 Package
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value">7</div>
                    <div class="stat-label">VIP6 Invites</div>
                    <div style="color: var(--accent-green); margin-top: 0.5rem;">
                        <i class="fas fa-arrow-right"></i> Free VIP6 Package
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value">7</div>
                    <div class="stat-label">VIP7 Invites</div>
                    <div style="color: var(--accent-green); margin-top: 0.5rem;">
                        <i class="fas fa-arrow-right"></i> Free VIP7 Package
                    </div>
                </div>
            </div>
        </div>

        <!-- Referral Tips -->
        <div class="card">
            <h3 style="text-align: center; margin-bottom: 2rem;">
                <i class="fas fa-lightbulb"></i> Referral Success Tips
            </h3>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                <div style="padding: 1.5rem; background: rgba(0, 212, 170, 0.1); border-radius: 10px;">
                    <h4 style="color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-share-alt"></i> Share Widely
                    </h4>
                    <p style="opacity: 0.9;">
                        Share your referral link across multiple platforms: social media, messaging apps, forums, and communities.
                    </p>
                </div>
                
                <div style="padding: 1.5rem; background: rgba(0, 212, 170, 0.1); border-radius: 10px;">
                    <h4 style="color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-chart-line"></i> Explain Benefits
                    </h4>
                    <p style="opacity: 0.9;">
                        Highlight the guaranteed daily returns, automated withdrawals, and multiple package options when promoting.
                    </p>
                </div>
                
                <div style="padding: 1.5rem; background: rgba(0, 212, 170, 0.1); border-radius: 10px;">
                    <h4 style="color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-users"></i> Build Trust
                    </h4>
                    <p style="opacity: 0.9;">
                        Share your own success story and earnings to build credibility and trust with potential referrals.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function shareOnWhatsApp() {
    const link = document.getElementById('referralLink').value;
    const message = `🚀 Join XCapital - Next-Gen Crypto Investment Platform!\n\n💰 Guaranteed daily returns\n⚡ Instant withdrawals\n🎯 Multiple VIP packages\n\nJoin now: ${link}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function shareOnTelegram() {
    const link = document.getElementById('referralLink').value;
    const message = `🚀 Join XCapital - Next-Gen Crypto Investment Platform!\n\n💰 Guaranteed daily returns\n⚡ Instant withdrawals\n🎯 Multiple VIP packages\n\nJoin now: ${link}`;
    const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(link)}&text=${encodeURIComponent(message)}`;
    window.open(telegramUrl, '_blank');
}

function shareOnTwitter() {
    const link = document.getElementById('referralLink').value;
    const message = `🚀 Join XCapital - Next-Gen Crypto Investment Platform! 💰 Guaranteed daily returns ⚡ Instant withdrawals 🎯 Multiple VIP packages`;
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}&url=${encodeURIComponent(link)}`;
    window.open(twitterUrl, '_blank');
}

function shareOnFacebook() {
    const link = document.getElementById('referralLink').value;
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(link)}`;
    window.open(facebookUrl, '_blank');
}

// Initialize referral chart if we have data
{% if commissions %}
const commissionData = [
    {% for commission in commissions[-7:] %}
    {
        date: '{{ commission.created_at.strftime("%m/%d") }}',
        amount: {{ commission.amount }},
        level: {{ commission.level }}
    },
    {% endfor %}
];

// Group by date
const groupedData = {};
commissionData.forEach(item => {
    if (!groupedData[item.date]) {
        groupedData[item.date] = 0;
    }
    groupedData[item.date] += item.amount;
});

const dates = Object.keys(groupedData);
const amounts = Object.values(groupedData);

// Create chart if canvas exists
const ctx = document.getElementById('referralChart');
if (ctx) {
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [{
                label: 'Daily Commissions',
                data: amounts,
                borderColor: '#39FF14',
                backgroundColor: 'rgba(57, 255, 20, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    labels: {
                        color: '#FFFFFF'
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#FFFFFF'
                    },
                    grid: {
                        color: 'rgba(57, 255, 20, 0.1)'
                    }
                },
                y: {
                    ticks: {
                        color: '#FFFFFF'
                    },
                    grid: {
                        color: 'rgba(57, 255, 20, 0.1)'
                    }
                }
            }
        }
    });
}
{% endif %}
</script>
{% endblock %}
