{% extends "base.html" %}

{% block title %}Admin Dashboard - XCapital{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="section" style="padding-top: 8rem;">
    <div class="container">
        <!-- Admin <PERSON>er -->
        <div class="card" style="text-align: center; background: var(--gradient-dark); margin-bottom: 3rem;">
            <h1 style="margin-bottom: 1rem;">
                <i class="fas fa-cogs"></i> Admin Dashboard
            </h1>
            <p style="font-size: 1.2rem; opacity: 0.9;">
                Complete platform management and oversight
            </p>
        </div>

        <!-- System Statistics -->
        <div class="stats-grid">
            <div class="stat-card glow">
                <div class="stat-value">{{ total_users }}</div>
                <div class="stat-label">
                    <i class="fas fa-users"></i> Total Users
                </div>
            </div>
            
            <div class="stat-card glow">
                <div class="stat-value">{{ active_users }}</div>
                <div class="stat-label">
                    <i class="fas fa-user-check"></i> Active Users
                </div>
            </div>
            
            <div class="stat-card glow">
                <div class="stat-value">{{ total_investments }}</div>
                <div class="stat-label">
                    <i class="fas fa-gem"></i> Total Investments
                </div>
            </div>
            
            <div class="stat-card glow">
                <div class="stat-value">${{ "%.2f"|format(total_invested) }}</div>
                <div class="stat-label">
                    <i class="fas fa-dollar-sign"></i> Total Invested (USDT)
                </div>
            </div>
            
            <div class="stat-card glow">
                <div class="stat-value">${{ "%.2f"|format(total_withdrawn) }}</div>
                <div class="stat-label">
                    <i class="fas fa-money-bill-wave"></i> Total Withdrawn (USDT)
                </div>
            </div>
            
            <div class="stat-card glow-accent">
                <div class="stat-value">{{ pending_withdrawals }}</div>
                <div class="stat-label">
                    <i class="fas fa-hourglass-half"></i> Pending Withdrawals
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <h3 style="margin-bottom: 2rem; text-align: center;">
                <i class="fas fa-bolt"></i> Quick Actions
            </h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem;">
                <button class="btn" onclick="processAllWithdrawals()" style="text-align: center; padding: 1.5rem;">
                    <i class="fas fa-play" style="font-size: 1.5rem; display: block; margin-bottom: 0.5rem;"></i>
                    Process Withdrawals
                </button>
                
                <button class="btn btn-secondary" onclick="runDailyEarnings()" style="text-align: center; padding: 1.5rem;">
                    <i class="fas fa-calculator" style="font-size: 1.5rem; display: block; margin-bottom: 0.5rem;"></i>
                    Run Daily Earnings
                </button>
                
                <button class="btn btn-secondary" onclick="exportUsers()" style="text-align: center; padding: 1.5rem;">
                    <i class="fas fa-download" style="font-size: 1.5rem; display: block; margin-bottom: 0.5rem;"></i>
                    Export Users
                </button>
                
                <button class="btn btn-secondary" onclick="showSystemSettings()" style="text-align: center; padding: 1.5rem;">
                    <i class="fas fa-cog" style="font-size: 1.5rem; display: block; margin-bottom: 0.5rem;"></i>
                    System Settings
                </button>
            </div>
        </div>

        <!-- Recent Users -->
        <div class="card">
            <h3 style="margin-bottom: 2rem;">
                <i class="fas fa-user-plus"></i> Recent User Registrations
            </h3>
            
            <div class="table">
                <table style="width: 100%;">
                    <thead>
                        <tr>
                            <th><i class="fas fa-id-card"></i> ID</th>
                            <th><i class="fas fa-user"></i> Username</th>
                            <th><i class="fas fa-envelope"></i> Email</th>
                            <th><i class="fas fa-link"></i> Referral Code</th>
                            <th><i class="fas fa-calendar"></i> Joined</th>
                            <th><i class="fas fa-toggle-on"></i> Status</th>
                            <th><i class="fas fa-tools"></i> Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in recent_users %}
                        <tr>
                            <td>{{ user.id }}</td>
                            <td>
                                <span style="color: var(--primary-green); font-weight: bold;">
                                    {{ user.username }}
                                </span>
                            </td>
                            <td>{{ user.email }}</td>
                            <td>
                                <code style="background: var(--secondary-black); padding: 0.2rem 0.5rem; border-radius: 3px;">
                                    {{ user.referral_code }}
                                </code>
                            </td>
                            <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                            <td>
                                {% if user.is_active %}
                                    <span style="color: var(--accent-green);">
                                        <i class="fas fa-check-circle"></i> Active
                                    </span>
                                {% else %}
                                    <span style="color: #ff4343;">
                                        <i class="fas fa-times-circle"></i> Inactive
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <button class="btn btn-secondary" onclick="toggleUserStatus({{ user.id }})" style="padding: 0.5rem 1rem; font-size: 0.9rem;">
                                    <i class="fas fa-toggle-{{ 'off' if user.is_active else 'on' }}"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Recent Investments -->
        <div class="card">
            <h3 style="margin-bottom: 2rem;">
                <i class="fas fa-gem"></i> Recent Investments
            </h3>
            
            <div class="table">
                <table style="width: 100%;">
                    <thead>
                        <tr>
                            <th><i class="fas fa-id-card"></i> ID</th>
                            <th><i class="fas fa-user"></i> User</th>
                            <th><i class="fas fa-gem"></i> Package</th>
                            <th><i class="fas fa-dollar-sign"></i> Amount</th>
                            <th><i class="fas fa-chart-line"></i> Daily Return</th>
                            <th><i class="fas fa-calendar"></i> Created</th>
                            <th><i class="fas fa-clock"></i> Expires</th>
                            <th><i class="fas fa-signal"></i> Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for investment in recent_investments %}
                        <tr>
                            <td>{{ investment.id }}</td>
                            <td>
                                <span style="color: var(--primary-green);">
                                    {{ investment.user.username }}
                                </span>
                            </td>
                            <td>
                                <span style="color: var(--accent-green); font-weight: bold;">
                                    {{ investment.package.name }}
                                </span>
                            </td>
                            <td>${{ "%.2f"|format(investment.amount) }}</td>
                            <td>
                                <span style="color: var(--accent-green);">
                                    ${{ "%.2f"|format(investment.daily_return) }}
                                </span>
                            </td>
                            <td>{{ investment.created_at.strftime('%Y-%m-%d') }}</td>
                            <td>{{ investment.expires_at.strftime('%Y-%m-%d') }}</td>
                            <td>
                                {% if investment.is_active %}
                                    <span style="color: var(--accent-green);">
                                        <i class="fas fa-check-circle"></i> Active
                                    </span>
                                {% else %}
                                    <span style="color: #ff4343;">
                                        <i class="fas fa-times-circle"></i> Expired
                                    </span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Recent Withdrawals -->
        <div class="card">
            <h3 style="margin-bottom: 2rem;">
                <i class="fas fa-money-bill-wave"></i> Recent Withdrawal Requests
            </h3>
            
            <div class="table">
                <table style="width: 100%;">
                    <thead>
                        <tr>
                            <th><i class="fas fa-id-card"></i> ID</th>
                            <th><i class="fas fa-user"></i> User</th>
                            <th><i class="fas fa-dollar-sign"></i> Amount</th>
                            <th><i class="fas fa-wallet"></i> Wallet</th>
                            <th><i class="fas fa-calendar"></i> Requested</th>
                            <th><i class="fas fa-info-circle"></i> Status</th>
                            <th><i class="fas fa-tools"></i> Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for withdrawal in recent_withdrawals %}
                        <tr>
                            <td>{{ withdrawal.id }}</td>
                            <td>
                                <span style="color: var(--primary-green);">
                                    {{ withdrawal.user.username }}
                                </span>
                            </td>
                            <td>${{ "%.2f"|format(withdrawal.amount) }}</td>
                            <td>
                                <code style="background: var(--secondary-black); padding: 0.2rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">
                                    {{ withdrawal.wallet_address[:10] }}...{{ withdrawal.wallet_address[-6:] }}
                                </code>
                            </td>
                            <td>{{ withdrawal.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                {% if withdrawal.status == 'completed' %}
                                    <span style="color: var(--accent-green);">
                                        <i class="fas fa-check-circle"></i> Completed
                                    </span>
                                {% elif withdrawal.status == 'processing' %}
                                    <span style="color: var(--primary-green);">
                                        <i class="fas fa-spinner"></i> Processing
                                    </span>
                                {% elif withdrawal.status == 'pending' %}
                                    <span style="color: #ffaa00;">
                                        <i class="fas fa-hourglass-half"></i> Pending
                                    </span>
                                {% else %}
                                    <span style="color: #ff4343;">
                                        <i class="fas fa-times-circle"></i> Failed
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                {% if withdrawal.status == 'pending' %}
                                    <button class="btn" onclick="processWithdrawal({{ withdrawal.id }})" style="padding: 0.5rem 1rem; font-size: 0.9rem;">
                                        <i class="fas fa-play"></i> Process
                                    </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Platform Health -->
        <div class="card" style="background: var(--gradient-dark);">
            <h3 style="text-align: center; margin-bottom: 2rem;">
                <i class="fas fa-heartbeat"></i> Platform Health
            </h3>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" style="color: var(--accent-green);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-label">Payment Gateway</div>
                    <small style="color: var(--accent-green);">NowPayments Active</small>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value" style="color: var(--accent-green);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-label">Withdrawal System</div>
                    <small style="color: var(--accent-green);">Coinremitter Active</small>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value" style="color: var(--accent-green);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-label">Database</div>
                    <small style="color: var(--accent-green);">PostgreSQL Online</small>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value" style="color: var(--accent-green);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-label">Daily Scheduler</div>
                    <small style="color: var(--accent-green);">Running (8:00 AM EST)</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function processWithdrawal(withdrawalId) {
    if (confirm('Are you sure you want to process this withdrawal?')) {
        showLoading('Processing withdrawal...');
        
        fetch('/admin/process-withdrawal', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                withdrawal_id: withdrawalId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Withdrawal processed successfully!', 'success');
                location.reload();
            } else {
                showNotification('Failed to process withdrawal: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Error processing withdrawal', 'error');
        });
    }
}

function processAllWithdrawals() {
    if (confirm('Are you sure you want to process all pending withdrawals?')) {
        showLoading('Processing all withdrawals...');
        
        fetch('/admin/process-all-withdrawals', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Processed ${data.count} withdrawals successfully!`, 'success');
                location.reload();
            } else {
                showNotification('Failed to process withdrawals: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Error processing withdrawals', 'error');
        });
    }
}

function runDailyEarnings() {
    if (confirm('Are you sure you want to run daily earnings processing?')) {
        showLoading('Processing daily earnings...');
        
        fetch('/admin/run-daily-earnings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Processed ${data.count} earnings successfully!`, 'success');
                location.reload();
            } else {
                showNotification('Failed to process earnings: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Error processing earnings', 'error');
        });
    }
}

function toggleUserStatus(userId) {
    if (confirm('Are you sure you want to toggle this user\'s status?')) {
        fetch('/admin/toggle-user-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('User status updated successfully!', 'success');
                location.reload();
            } else {
                showNotification('Failed to update user status: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Error updating user status', 'error');
        });
    }
}

function exportUsers() {
    window.location.href = '/admin/export-users';
}

function showSystemSettings() {
    showNotification('System settings feature coming soon!', 'info');
}

function showLoading(message) {
    showNotification(message, 'info');
}
</script>
{% endblock %}
