# Cryptomus API Setup Guide - XCapital

## ¿Por qué Cryptomus?

Cryptomus es la mejor opción para pagos automáticos de criptomonedas porque ofrece:

- ✅ **0% de comisiones en retiros** - Completamente gratis
- ✅ **API gratuita** - Sin costos mensuales
- ✅ **Pagos automáticos** - Sin intervención manual
- ✅ **Soporte USDT TRC20** - La criptomoneda más popular
- ✅ **Conversión automática** - De USD a USDT automáticamente
- ✅ **Webhooks en tiempo real** - Confirmaciones instantáneas

## Configuración Paso a Paso

### 1. Crear Cuenta en Cryptomus

1. Ve a [https://cryptomus.com/](https://cryptomus.com/)
2. Haz clic en "Sign Up"
3. Completa el registro con tu email
4. Verifica tu cuenta por email

### 2. Configurar Merchant

1. Inicia sesión en tu cuenta Cryptomus
2. Ve a "Business" → "Settings"
3. Completa la información de tu negocio:
   - Business Name: "XCapital"
   - Website: tu dominio
   - Description: "Cryptocurrency Investment Platform"

### 3. Obtener API Keys

#### Para Pagos (Payment API Key):
1. Ve a "Business" → "API Keys"
2. Haz clic en "Generate Payment API Key"
3. Copia el **Merchant ID** y **Payment API Key**

#### Para Retiros (Payout API Key):
1. En la misma sección, haz clic en "Generate Payout API Key"
2. **IMPORTANTE**: Los retiros se bloquean por 24 horas después de generar esta clave
3. Copia el **Payout API Key**

### 4. Configurar Variables de Entorno

Actualiza tu archivo `.env` con los valores obtenidos:

```bash
# Cryptomus Configuration
CRYPTOMUS_MERCHANT_ID=tu-merchant-id-aqui
CRYPTOMUS_PAYMENT_API_KEY=tu-payment-api-key-aqui
CRYPTOMUS_PAYOUT_API_KEY=tu-payout-api-key-aqui
CRYPTOMUS_BASE_URL=https://api.cryptomus.com/v1
```

### 5. Configurar Webhooks

En tu panel de Cryptomus:

1. Ve a "Business" → "Webhooks"
2. Configura las siguientes URLs:

**Para Pagos:**
- URL: `https://tu-dominio.com/webhook/cryptomus/payment`
- Eventos: Payment status changes

**Para Retiros:**
- URL: `https://tu-dominio.com/webhook/cryptomus/payout`
- Eventos: Payout status changes

### 6. Configurar Wallets

1. Ve a "Business" → "Wallets"
2. Activa las siguientes criptomonedas:
   - **USDT (TRC20)** - Principal para retiros
   - **USDT (ERC20)** - Alternativa
   - **BTC** - Opcional
   - **ETH** - Opcional

### 7. Configurar Auto-Convert

Para convertir automáticamente todos los pagos a USDT:

1. Ve a "Business" → "Settings" → "Auto-convert"
2. Activa "Auto-convert to USDT"
3. Esto asegura que tengas USDT disponible para retiros

## Flujo de Funcionamiento

### Recibir Pagos:
1. Usuario selecciona paquete VIP
2. Se crea invoice en Cryptomus (USD → USDT)
3. Usuario paga con cualquier cripto soportada
4. Cryptomus convierte automáticamente a USDT
5. Webhook confirma el pago
6. Se activa la inversión del usuario

### Procesar Retiros:
1. Usuario solicita retiro
2. Sistema crea payout automático en Cryptomus
3. Cryptomus procesa el retiro (0% comisión)
4. Webhook confirma el envío
5. Se actualiza el estado del retiro

## Ventajas vs Otras APIs

| Característica | Cryptomus | NowPayments | Coinremitter |
|----------------|-----------|-------------|--------------|
| Comisión retiros | 0% | 0.5-1% | 1-2% |
| API gratuita | ✅ | ✅ | ❌ |
| Pagos automáticos | ✅ | ❌ | ✅ |
| USDT TRC20 | ✅ | ✅ | ✅ |
| Conversión auto | ✅ | ❌ | ❌ |

## Límites y Restricciones

- **Mínimo retiro**: 0.5 USDT
- **Máximo retiro**: 10,000,000 USDT
- **Tiempo procesamiento**: 5-30 minutos
- **Bloqueo temporal**: 24h después de cambiar payout API key

## Soporte y Documentación

- **Documentación oficial**: [https://doc.cryptomus.com/](https://doc.cryptomus.com/)
- **Soporte**: <EMAIL>
- **Telegram**: @cryptomus_support

## Troubleshooting

### Error: "Wallet not found"
- Asegúrate de tener una wallet USDT activa
- Ve a Business → Wallets y activa USDT (TRC20)

### Error: "Not enough funds"
- Verifica tu balance en el dashboard
- Asegúrate de tener suficiente USDT para el retiro

### Error: "You are forbidden"
- Espera 24 horas después de generar payout API key
- Verifica que tu cuenta esté completamente verificada

### Webhook no funciona
- Verifica que las URLs sean accesibles públicamente
- Revisa los logs de tu aplicación
- Confirma que la firma del webhook sea válida
