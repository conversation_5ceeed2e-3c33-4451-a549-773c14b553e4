# XCapital - Python Dependencies
# Install with: pip install -r requirements.txt

# Core Flask Framework
Flask>=3.1.1
Flask-SQLAlchemy>=3.1.1
Flask-Login>=0.6.3
Flask-Dance>=7.1.0

# Database Drivers
psycopg2-binary>=2.9.10  # PostgreSQL
mysql-connector-python>=9.3.0  # MySQL
PyMySQL>=1.1.1  # MySQL alternative driver
SQLAlchemy>=2.0.41

# Web Server
gunicorn>=23.0.0
Werkzeug>=3.1.3

# Authentication & Security
oauthlib>=3.3.1
PyJWT>=2.10.1

# Background Tasks
APScheduler>=3.11.0

# HTTP Requests
requests>=2.32.4

# Email Validation
email-validator>=2.2.0

# Environment Variables
python-dotenv>=1.0.0
