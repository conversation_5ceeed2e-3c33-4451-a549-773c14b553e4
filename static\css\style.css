@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

:root {
    --primary-green: #00D4AA;
    --secondary-black: #1A1A1A;
    --accent-green: #39FF14;
    --background-navy: #0D1421;
    --text-white: #FFFFFF;
    --card-grey: #1E2329;
    --gradient-primary: linear-gradient(135deg, #00D4AA 0%, #39FF14 100%);
    --gradient-dark: linear-gradient(135deg, #0D1421 0%, #1A1A1A 100%);
    --shadow-glow: 0 0 20px rgba(0, 212, 170, 0.3);
    --shadow-glow-accent: 0 0 20px rgba(57, 255, 20, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Rajdhani', sans-serif;
    background: var(--background-navy);
    color: var(--text-white);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Background Animation */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 50%, rgba(0, 212, 170, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(57, 255, 20, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(0, 212, 170, 0.05) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundPulse 10s ease-in-out infinite alternate;
}

@keyframes backgroundPulse {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
}

h1 {
    font-size: 3rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    margin-bottom: 2rem;
}

h2 {
    font-size: 2.5rem;
    color: var(--primary-green);
    margin-bottom: 1.5rem;
}

h3 {
    font-size: 2rem;
    color: var(--accent-green);
    margin-bottom: 1rem;
}

/* Navigation */
.navbar {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid var(--primary-green);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.logo {
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    color: var(--text-white);
    text-decoration: none;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-links a:hover {
    color: var(--primary-green);
    text-shadow: 0 0 10px var(--primary-green);
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Cards */
.card {
    background: var(--card-grey);
    border-radius: 15px;
    padding: 2rem;
    margin: 1rem 0;
    border: 1px solid rgba(0, 212, 170, 0.2);
    box-shadow: var(--shadow-glow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 170, 0.1), transparent);
    transition: left 0.5s ease;
}

.card:hover::before {
    left: 100%;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-glow-accent);
    border-color: var(--accent-green);
}

/* VIP Package Cards */
.vip-card {
    background: linear-gradient(135deg, var(--card-grey) 0%, rgba(26, 26, 26, 0.9) 100%);
    border: 2px solid var(--primary-green);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.vip-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, var(--primary-green), transparent);
    animation: rotate 4s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.vip-card:hover::before {
    opacity: 0.1;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.vip-card-content {
    position: relative;
    z-index: 1;
}

.vip-level {
    font-family: 'Orbitron', monospace;
    font-size: 2rem;
    font-weight: 900;
    color: var(--accent-green);
    text-shadow: 0 0 10px var(--accent-green);
    margin-bottom: 1rem;
}

.vip-price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-green);
    margin-bottom: 1rem;
}

.vip-daily {
    font-size: 1.5rem;
    color: var(--text-white);
    margin-bottom: 2rem;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    background: var(--gradient-primary);
    color: var(--secondary-black);
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 212, 170, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-green);
    border: 2px solid var(--primary-green);
}

.btn-secondary:hover {
    background: var(--primary-green);
    color: var(--secondary-black);
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--primary-green);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.form-input {
    width: 100%;
    padding: 1rem;
    background: rgba(26, 26, 26, 0.8);
    border: 2px solid rgba(0, 212, 170, 0.3);
    border-radius: 10px;
    color: var(--text-white);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
}

/* Tables */
.table {
    width: 100%;
    background: var(--card-grey);
    border-radius: 10px;
    overflow: hidden;
    margin: 2rem 0;
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(0, 212, 170, 0.1);
}

.table th {
    background: var(--secondary-black);
    color: var(--primary-green);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.table tr:hover {
    background: rgba(0, 212, 170, 0.05);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.stat-card {
    background: var(--card-grey);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(0, 212, 170, 0.2);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-glow);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-green);
    font-family: 'Orbitron', monospace;
}

.stat-label {
    font-size: 1rem;
    color: var(--text-white);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 0.5rem;
}

/* Package Grid */
.packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 8rem 0 4rem;
    position: relative;
}

.hero-content {
    max-width: 800px;
    z-index: 1;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--primary-green);
    margin-bottom: 2rem;
    font-weight: 300;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    line-height: 1.8;
    opacity: 0.9;
}

/* Content Sections */
.section {
    padding: 6rem 0;
}

.section-title {
    text-align: center;
    margin-bottom: 4rem;
}

/* Alerts */
.alert {
    padding: 1rem 1.5rem;
    border-radius: 10px;
    margin: 1rem 0;
    position: relative;
    border-left: 4px solid;
}

.alert-success {
    background: rgba(0, 212, 170, 0.1);
    border-color: var(--primary-green);
    color: var(--primary-green);
}

.alert-error {
    background: rgba(255, 67, 67, 0.1);
    border-color: #ff4343;
    color: #ff4343;
}

.alert-info {
    background: rgba(57, 255, 20, 0.1);
    border-color: var(--accent-green);
    color: var(--accent-green);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 212, 170, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-green);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-links {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.8rem;
    }
    
    .hero {
        padding: 6rem 0 2rem;
    }
    
    .stats-grid,
    .packages-grid {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: 0 1rem;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: var(--background-navy);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-green);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-green);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.glow { box-shadow: var(--shadow-glow); }
.glow-accent { box-shadow: var(--shadow-glow-accent); }
