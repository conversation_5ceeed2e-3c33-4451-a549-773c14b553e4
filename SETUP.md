# XCapital - Setup Guide

## Environment Variables Setup

### 1. Install Dependencies

Choose one of the following methods:

#### Using UV (Recommended)
```bash
uv sync
```

#### Using pip
```bash
pip install -r requirements.txt
```

### 2. Environment Variables Configuration

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file and fill in your actual values:
   ```bash
   nano .env
   # or
   code .env
   ```

### 3. Required Environment Variables

#### Database Configuration
- **DATABASE_URL**: Your database connection string
  - PostgreSQL: `postgresql://username:password@localhost:5432/xcapital_db`
  - MySQL: `mysql+pymysql://username:password@localhost:3306/xcapital_db`
  - SQLite: `sqlite:///xcapital.db` (development only)

#### Flask Configuration
- **SESSION_SECRET**: Secret key for Flask sessions
  - Generate with: `python -c "import secrets; print(secrets.token_hex(32))"`
- **APP_URL**: Your application's base URL (for webhooks)

#### Payment Processors
- **NOWPAYMENTS_API_KEY**: API key from NowPayments.io (for receiving payments)
- **NOWPAYMENTS_PUBLIC_KEY**: Public key from NowPayments.io
- **CRYPTOMUS_MERCHANT_ID**: Merchant ID from Cryptomus (for automatic withdrawals)
- **CRYPTOMUS_PAYMENT_API_KEY**: Payment API key from Cryptomus
- **CRYPTOMUS_PAYOUT_API_KEY**: Payout API key from Cryptomus
- **COINREMITTER_API_KEY**: API key from Coinremitter (fallback)
- **COINREMITTER_PASSWORD**: Password for Coinremitter (fallback)

### 4. Database Setup

1. Create your database (PostgreSQL/MySQL)
2. Update DATABASE_URL in your .env file
3. Run the application to create tables automatically

### 5. Running the Application

#### Development
```bash
python main.py
```

#### Production
```bash
gunicorn --bind 0.0.0.0:5000 main:app
```

### 6. Default Admin Account

The application creates a default admin account:
- **Username**: admin
- **Email**: <EMAIL>
- **Password**: admin123

**⚠️ Important**: Change the admin password immediately after first login!

## Payment Processors Setup

### NowPayments.io (For Receiving Payments)
Your current configuration:
- API Key: `3PRY7HJ-SNFMG8V-HZT3VX6-QW6Y5Y8`
- Public Key: `c83f4444-df64-486a-94b2-898373553ef8`

### Cryptomus (For Automatic Withdrawals - RECOMMENDED)
**Why Cryptomus?**
- ✅ 0% withdrawal fees
- ✅ Free API
- ✅ Automatic processing
- ✅ USDT TRC20 support

**Setup Instructions:**
1. Create account at [https://cryptomus.com/](https://cryptomus.com/)
2. Get your Merchant ID and API keys
3. See `CRYPTOMUS_SETUP.md` for detailed instructions

### MySQL Database
Your current configuration:
- Database: `u853572638_Xcapital`
- User: `u853572638_Xcapital`
- Password: `28VBnlf^4$qF`

## Security Notes

1. Never commit your `.env` file to version control
2. Use strong, unique passwords for all accounts
3. Generate a secure SESSION_SECRET
4. Use HTTPS in production
5. Regularly update dependencies

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check your DATABASE_URL format
   - Ensure database server is running
   - Verify credentials

2. **Missing Environment Variables**
   - Ensure .env file exists
   - Check variable names match exactly
   - Restart application after changes

3. **Payment Integration Issues**
   - Verify API keys are correct
   - Check webhook URLs are accessible
   - Review payment processor documentation
