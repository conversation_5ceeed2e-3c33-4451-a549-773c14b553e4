from datetime import datetime, timedelta
from app import db
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
import uuid

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(256), nullable=False)
    usdt_wallet = db.Column(db.String(100), nullable=True)
    balance = db.Column(db.Float, default=0.0)
    total_invested = db.Column(db.Float, default=0.0)
    total_earned = db.Column(db.Float, default=0.0)
    total_withdrawn = db.Column(db.Float, default=0.0)
    referral_code = db.Column(db.String(20), unique=True, nullable=False)
    referred_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    is_admin = db.Column(db.<PERSON>olean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)
    
    # Relationships
    investments = db.relationship('Investment', backref='user', lazy=True)
    withdrawals = db.relationship('Withdrawal', backref='user', lazy=True)
    commissions = db.relationship('Commission', foreign_keys='Commission.user_id', backref='user', lazy=True)
    referrals = db.relationship('User', backref=db.backref('referrer', remote_side=[id]), lazy=True)
    
    def __init__(self, **kwargs):
        super(User, self).__init__(**kwargs)
        if not self.referral_code:
            self.referral_code = str(uuid.uuid4())[:8].upper()
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def get_active_investments(self):
        return Investment.query.filter_by(
            user_id=self.id, 
            is_active=True
        ).all()
    
    def get_total_daily_earnings(self):
        total = 0
        for investment in self.get_active_investments():
            if investment.is_active and investment.expires_at > datetime.utcnow():
                total += investment.daily_return
        return total
    
    def get_referral_stats(self):
        level1 = User.query.filter_by(referred_by=self.id).count()
        level2 = 0
        level3 = 0
        
        for ref1 in User.query.filter_by(referred_by=self.id).all():
            level2 += User.query.filter_by(referred_by=ref1.id).count()
            for ref2 in User.query.filter_by(referred_by=ref1.id).all():
                level3 += User.query.filter_by(referred_by=ref2.id).count()
        
        return {'level1': level1, 'level2': level2, 'level3': level3}

class VIPPackage(db.Model):
    __tablename__ = 'vip_packages'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)  # VIP0, VIP1, etc.
    price = db.Column(db.Float, nullable=False)
    daily_return = db.Column(db.Float, nullable=False)
    duration_days = db.Column(db.Integer, default=90)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Investment(db.Model):
    __tablename__ = 'investments'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    package_id = db.Column(db.Integer, db.ForeignKey('vip_packages.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    daily_return = db.Column(db.Float, nullable=False)
    total_earned = db.Column(db.Float, default=0.0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime, nullable=False)
    last_earning_date = db.Column(db.Date, nullable=True)
    
    # Relationship
    package = db.relationship('VIPPackage', backref='investments')
    
    def __init__(self, **kwargs):
        super(Investment, self).__init__(**kwargs)
        if not self.expires_at:
            package = VIPPackage.query.get(kwargs.get('package_id'))
            if package:
                self.expires_at = datetime.utcnow() + timedelta(days=package.duration_days)
            else:
                self.expires_at = datetime.utcnow() + timedelta(days=90)

class Withdrawal(db.Model):
    __tablename__ = 'withdrawals'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    wallet_address = db.Column(db.String(100), nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, processing, completed, failed
    transaction_id = db.Column(db.String(100), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    processed_at = db.Column(db.DateTime, nullable=True)
    notes = db.Column(db.Text, nullable=True)

class Commission(db.Model):
    __tablename__ = 'commissions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    from_user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    investment_id = db.Column(db.Integer, db.ForeignKey('investments.id'), nullable=False)
    level = db.Column(db.Integer, nullable=False)  # 1, 2, or 3
    rate = db.Column(db.Float, nullable=False)  # 0.10, 0.05, 0.01
    amount = db.Column(db.Float, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    from_user = db.relationship('User', foreign_keys=[from_user_id])
    investment = db.relationship('Investment', backref='commissions')

class DailyEarning(db.Model):
    __tablename__ = 'daily_earnings'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    investment_id = db.Column(db.Integer, db.ForeignKey('investments.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    earning_date = db.Column(db.Date, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    investment = db.relationship('Investment', backref='earnings')

class Payment(db.Model):
    __tablename__ = 'payments'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    package_id = db.Column(db.Integer, db.ForeignKey('vip_packages.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    payment_id = db.Column(db.String(100), unique=True, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, confirmed, failed, expired
    payment_url = db.Column(db.String(500), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    confirmed_at = db.Column(db.DateTime, nullable=True)
    
    # Relationships
    package = db.relationship('VIPPackage', backref='payments')

class SystemSettings(db.Model):
    __tablename__ = 'system_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text, nullable=False)
    description = db.Column(db.String(255), nullable=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
