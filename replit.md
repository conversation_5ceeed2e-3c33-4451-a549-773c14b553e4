# XCapital - Crypto Investment Platform

## Overview

XCapital is a comprehensive cryptocurrency investment platform built with Flask. The application provides VIP investment packages, referral systems, automated daily earnings, and payment processing capabilities. Users can invest in different VIP levels, earn daily returns, refer others for commissions, and withdraw funds through USDT.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Template Engine**: Jinja2 templates with Flask
- **Styling**: Custom CSS with modern design using CSS variables and animations
- **JavaScript**: Vanilla JavaScript for interactive elements, charts (Chart.js), and form handling
- **Responsive Design**: Mobile-first approach with grid layouts and flexible components

### Backend Architecture
- **Framework**: Flask web application
- **Authentication**: Flask-Login with session management
- **Database ORM**: SQLAlchemy with declarative models
- **Background Tasks**: APScheduler for automated daily earnings processing
- **Payment Processing**: Integration with NowPayments.io and Coinremitter APIs
- **Security**: Password hashing with Werkzeug, admin/active user decorators

### Data Storage Solutions
- **Primary Database**: SQLAlchemy with configurable database URI (supports PostgreSQL, MySQL, SQLite)
- **Models**: User, Investment, VIPPackage, Payment, Withdrawal, Commission, DailyEarning, SystemSettings
- **Connection Pooling**: Configured with pre-ping and connection recycling for reliability

## Key Components

### Authentication System
- **User Management**: Registration, login, logout with Flask-Login
- **Role-Based Access**: Admin and regular user roles with decorator-based protection
- **Session Security**: Secret key-based session management
- **Account Status**: Active/inactive user status checking

### Investment System
- **VIP Packages**: 11 different investment levels (VIP0-VIP10) with varying amounts and returns
- **Daily Earnings**: Automated processing of daily returns for active investments
- **Investment Tracking**: Complete investment history and status monitoring

### Referral Program
- **Multi-Level**: 3-tier referral system (10%, 5%, 1% commissions)
- **Lifetime Earnings**: Permanent commission structure
- **Referral Codes**: Unique codes generated for each user

### Payment Processing
- **Crypto Payments**: USDT support through NowPayments.io and Coinremitter
- **Withdrawal System**: Automated USDT withdrawals with instant processing
- **Payment Tracking**: Complete transaction history and status monitoring

### Administrative Features
- **Admin Dashboard**: Complete platform oversight and management
- **User Management**: Admin controls for user accounts and settings
- **System Statistics**: Platform-wide analytics and reporting

## Data Flow

1. **User Registration**: Creates account with referral code, optional wallet address
2. **Investment Process**: User selects VIP package → Payment processing → Investment activation
3. **Daily Earnings**: Scheduled task processes daily returns → Updates user balance
4. **Referral Commissions**: Investment triggers commission calculations for referrer chain
5. **Withdrawal Flow**: User requests withdrawal → Automated processing → USDT transfer

## External Dependencies

### Payment Providers
- **NowPayments.io**: Primary payment processor for USDT investments
- **Coinremitter**: Alternative payment processing option
- **Webhook Integration**: Real-time payment status updates

### Libraries and Frameworks
- **Flask**: Core web framework
- **SQLAlchemy**: Database ORM
- **Flask-Login**: User session management
- **APScheduler**: Background task scheduling
- **Chart.js**: Frontend data visualization
- **Werkzeug**: Security utilities and WSGI utilities

### Environment Configuration
- **DATABASE_URL**: Database connection string
- **SESSION_SECRET**: Flask session encryption key
- **NOWPAYMENTS_API_KEY**: Payment processor API credentials
- **COINREMITTER_API_KEY**: Alternative payment processor credentials
- **APP_URL**: Application base URL for webhooks

## Deployment Strategy

### Application Structure
- **Entry Point**: main.py initializes Flask app and imports routes/scheduler
- **Modular Design**: Separated concerns with dedicated files for models, routes, auth, payments
- **Static Assets**: CSS and JavaScript served from static directory
- **Template Organization**: Jinja2 templates with base template inheritance

### Production Considerations
- **WSGI Configuration**: ProxyFix middleware for proper header handling
- **Database Connection**: Pool management with health checks and recycling
- **Logging**: Configured logging for debugging and monitoring
- **Background Tasks**: APScheduler runs daily earnings processing
- **Security**: Environment-based configuration for sensitive data

### Scaling Readiness
- **Database Agnostic**: SQLAlchemy supports multiple database backends
- **Session Management**: Stateless design with secure session handling
- **API Integration**: External payment processors for transaction handling
- **Automated Processing**: Background tasks reduce manual intervention requirements