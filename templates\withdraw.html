{% extends "base.html" %}

{% block title %}Withdraw Funds - XCapital{% endblock %}

{% block content %}
<div class="section" style="padding-top: 8rem;">
    <div class="container">
        <!-- Header -->
        <div class="card" style="text-align: center; background: var(--gradient-dark); margin-bottom: 3rem;">
            <h1 style="margin-bottom: 1rem;">
                <i class="fas fa-money-bill-wave"></i> Withdraw Your Funds
            </h1>
            <p style="font-size: 1.2rem; opacity: 0.9; margin-bottom: 2rem;">
                Instant USDT withdrawals processed automatically in under 1 minute
            </p>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">${{ "%.2f"|format(current_user.balance) }}</div>
                    <div class="stat-label">Available Balance (USDT)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${{ "%.2f"|format(current_user.total_withdrawn) }}</div>
                    <div class="stat-label">Total Withdrawn (USDT)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">< 1</div>
                    <div class="stat-label">Minute Processing</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">24/7</div>
                    <div class="stat-label">Always Available</div>
                </div>
            </div>
        </div>

        <!-- Withdrawal Form -->
        <div class="card" style="max-width: 600px; margin: 0 auto;">
            <h3 style="text-align: center; margin-bottom: 2rem;">
                <i class="fas fa-paper-plane"></i> Request Withdrawal
            </h3>
            
            <form method="POST" id="withdrawalForm">
                <div class="form-group">
                    <label for="amount" class="form-label">
                        <i class="fas fa-dollar-sign"></i> Withdrawal Amount (USDT)
                    </label>
                    <input type="number" 
                           id="amount" 
                           name="amount" 
                           class="form-input" 
                           required 
                           min="1"
                           max="{{ current_user.balance }}"
                           step="0.01"
                           placeholder="Enter amount to withdraw">
                    <small style="color: var(--primary-green); font-size: 0.9rem; margin-top: 0.5rem; display: block;">
                        <i class="fas fa-info-circle"></i> 
                        Maximum: ${{ "%.2f"|format(current_user.balance) }} USDT
                    </small>
                </div>
                
                <div class="form-group">
                    <label for="wallet_address" class="form-label">
                        <i class="fas fa-wallet"></i> USDT Wallet Address
                    </label>
                    <input type="text" 
                           id="wallet_address" 
                           name="wallet_address" 
                           class="form-input" 
                           required
                           value="{{ current_user.usdt_wallet or '' }}"
                           placeholder="Enter your USDT BEP20/TRC20 wallet address">
                    <small style="color: var(--accent-green); font-size: 0.9rem; margin-top: 0.5rem; display: block;">
                        <i class="fas fa-shield-alt"></i> 
                        Supports both BEP20 (BSC) and TRC20 (Tron) networks
                    </small>
                </div>
                
                <!-- Quick Amount Buttons -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-zap"></i> Quick Amount Selection
                    </label>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 1rem;">
                        <button type="button" class="btn btn-secondary" onclick="setAmount(10)" style="padding: 0.8rem;">
                            $10
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="setAmount(50)" style="padding: 0.8rem;">
                            $50
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="setAmount(100)" style="padding: 0.8rem;">
                            $100
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="setAmount({{ current_user.balance|round(2) }})" style="padding: 0.8rem;">
                            All
                        </button>
                    </div>
                </div>
                
                <div class="form-group">
                    <div style="background: rgba(0, 212, 170, 0.1); padding: 1.5rem; border-radius: 10px; border: 1px solid var(--primary-green);">
                        <h4 style="color: var(--primary-green); margin-bottom: 1rem;">
                            <i class="fas fa-info-circle"></i> Withdrawal Information
                        </h4>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin-bottom: 0.5rem;">
                                <i class="fas fa-check" style="color: var(--accent-green); margin-right: 0.5rem;"></i>
                                Processing time: Under 1 minute
                            </li>
                            <li style="margin-bottom: 0.5rem;">
                                <i class="fas fa-check" style="color: var(--accent-green); margin-right: 0.5rem;"></i>
                                No withdrawal fees
                            </li>
                            <li style="margin-bottom: 0.5rem;">
                                <i class="fas fa-check" style="color: var(--accent-green); margin-right: 0.5rem;"></i>
                                Available 24/7, 365 days a year
                            </li>
                            <li style="margin-bottom: 0.5rem;">
                                <i class="fas fa-check" style="color: var(--accent-green); margin-right: 0.5rem;"></i>
                                Automatic processing via Coinremitter
                            </li>
                        </ul>
                    </div>
                </div>
                
                <button type="submit" class="btn" style="width: 100%; padding: 1.5rem; font-size: 1.1rem;" id="withdrawBtn">
                    <i class="fas fa-paper-plane"></i> Submit Withdrawal Request
                </button>
            </form>
        </div>

        <!-- Withdrawal History -->
        <div class="card">
            <h3 style="margin-bottom: 2rem;">
                <i class="fas fa-history"></i> Withdrawal History
            </h3>
            
            {% if withdrawals %}
                <div class="table">
                    <table style="width: 100%;">
                        <thead>
                            <tr>
                                <th><i class="fas fa-calendar"></i> Date</th>
                                <th><i class="fas fa-dollar-sign"></i> Amount</th>
                                <th><i class="fas fa-wallet"></i> Wallet Address</th>
                                <th><i class="fas fa-info-circle"></i> Status</th>
                                <th><i class="fas fa-clock"></i> Processed</th>
                                <th><i class="fas fa-receipt"></i> Transaction</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for withdrawal in withdrawals %}
                            <tr>
                                <td>{{ withdrawal.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <span style="color: var(--primary-green); font-weight: bold;">
                                        ${{ "%.2f"|format(withdrawal.amount) }} USDT
                                    </span>
                                </td>
                                <td>
                                    <code style="background: var(--secondary-black); padding: 0.2rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">
                                        {{ withdrawal.wallet_address[:10] }}...{{ withdrawal.wallet_address[-6:] }}
                                    </code>
                                </td>
                                <td>
                                    {% if withdrawal.status == 'completed' %}
                                        <span style="color: var(--accent-green);">
                                            <i class="fas fa-check-circle"></i> Completed
                                        </span>
                                    {% elif withdrawal.status == 'processing' %}
                                        <span style="color: var(--primary-green);">
                                            <i class="fas fa-spinner fa-spin"></i> Processing
                                        </span>
                                    {% elif withdrawal.status == 'pending' %}
                                        <span style="color: #ffaa00;">
                                            <i class="fas fa-hourglass-half"></i> Pending
                                        </span>
                                    {% else %}
                                        <span style="color: #ff4343;">
                                            <i class="fas fa-times-circle"></i> Failed
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if withdrawal.processed_at %}
                                        {{ withdrawal.processed_at.strftime('%Y-%m-%d %H:%M') }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if withdrawal.transaction_id %}
                                        <a href="#" style="color: var(--accent-green); text-decoration: none;" 
                                           onclick="showTransactionDetails('{{ withdrawal.transaction_id }}')">
                                            <i class="fas fa-external-link-alt"></i> View
                                        </a>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div style="text-align: center; padding: 3rem; opacity: 0.7;">
                    <i class="fas fa-money-bill-wave" style="font-size: 4rem; color: var(--primary-green); margin-bottom: 1rem;"></i>
                    <h4>No Withdrawals Yet</h4>
                    <p style="margin: 1rem 0;">Your withdrawal history will appear here</p>
                    <p style="opacity: 0.8;">Make your first withdrawal to see the lightning-fast processing!</p>
                </div>
            {% endif %}
        </div>

        <!-- Withdrawal FAQs -->
        <div class="card" style="background: var(--gradient-dark);">
            <h3 style="text-align: center; margin-bottom: 2rem;">
                <i class="fas fa-question-circle"></i> Withdrawal FAQs
            </h3>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                <div>
                    <h4 style="color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-clock"></i> How long does it take?
                    </h4>
                    <p style="opacity: 0.9;">
                        All withdrawals are processed automatically in under 1 minute. 
                        Our system uses the Coinremitter API for instant USDT transfers.
                    </p>
                </div>
                
                <div>
                    <h4 style="color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-dollar-sign"></i> Are there any fees?
                    </h4>
                    <p style="opacity: 0.9;">
                        No, there are no withdrawal fees. You receive exactly the amount you request, 
                        minus only the blockchain network transaction fee.
                    </p>
                </div>
                
                <div>
                    <h4 style="color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-wallet"></i> Which networks are supported?
                    </h4>
                    <p style="opacity: 0.9;">
                        We support both BEP20 (Binance Smart Chain) and TRC20 (Tron) networks 
                        for USDT withdrawals with the lowest fees.
                    </p>
                </div>
                
                <div>
                    <h4 style="color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-shield-alt"></i> Is it secure?
                    </h4>
                    <p style="opacity: 0.9;">
                        Yes, all withdrawals are secured with advanced encryption and processed 
                        through our trusted payment partner with multi-layer security.
                    </p>
                </div>
                
                <div>
                    <h4 style="color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-calendar"></i> When can I withdraw?
                    </h4>
                    <p style="opacity: 0.9;">
                        Withdrawals are available 24/7, 365 days a year. 
                        You can withdraw your earnings anytime without restrictions.
                    </p>
                </div>
                
                <div>
                    <h4 style="color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-coins"></i> Minimum withdrawal?
                    </h4>
                    <p style="opacity: 0.9;">
                        The minimum withdrawal amount is $1 USDT. 
                        There's no maximum limit - withdraw any amount you have available.
                    </p>
                </div>
            </div>
        </div>

        <!-- Supported Networks -->
        <div class="card">
            <h3 style="text-align: center; margin-bottom: 2rem;">
                <i class="fas fa-network-wired"></i> Supported Networks
            </h3>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                <div style="text-align: center; padding: 2rem; background: rgba(0, 212, 170, 0.1); border-radius: 15px; border: 2px solid var(--primary-green);">
                    <div style="font-size: 3rem; color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fab fa-ethereum"></i>
                    </div>
                    <h4 style="color: var(--primary-green); margin-bottom: 1rem;">BEP20 (BSC)</h4>
                    <p style="opacity: 0.9; margin-bottom: 1rem;">
                        Binance Smart Chain network with low fees and fast transactions
                    </p>
                    <div style="background: rgba(0, 0, 0, 0.3); padding: 1rem; border-radius: 8px;">
                        <strong>Network Fee:</strong> ~$0.20<br>
                        <strong>Speed:</strong> 3-5 seconds<br>
                        <strong>Reliability:</strong> 99.9%
                    </div>
                </div>
                
                <div style="text-align: center; padding: 2rem; background: rgba(0, 212, 170, 0.1); border-radius: 15px; border: 2px solid var(--primary-green);">
                    <div style="font-size: 3rem; color: var(--primary-green); margin-bottom: 1rem;">
                        <i class="fas fa-coins"></i>
                    </div>
                    <h4 style="color: var(--primary-green); margin-bottom: 1rem;">TRC20 (Tron)</h4>
                    <p style="opacity: 0.9; margin-bottom: 1rem;">
                        Tron network with ultra-low fees and instant confirmations
                    </p>
                    <div style="background: rgba(0, 0, 0, 0.3); padding: 1rem; border-radius: 8px;">
                        <strong>Network Fee:</strong> ~$0.10<br>
                        <strong>Speed:</strong> 1-3 seconds<br>
                        <strong>Reliability:</strong> 99.9%
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function setAmount(amount) {
    const maxAmount = {{ current_user.balance|round(2) }};
    const actualAmount = Math.min(amount, maxAmount);
    document.getElementById('amount').value = actualAmount.toFixed(2);
}

function showTransactionDetails(txId) {
    showNotification(`Transaction ID: ${txId}`, 'info');
}

document.getElementById('withdrawalForm').addEventListener('submit', function(e) {
    const amount = parseFloat(document.getElementById('amount').value);
    const maxAmount = {{ current_user.balance|round(2) }};
    const walletAddress = document.getElementById('wallet_address').value;
    
    // Validation
    if (amount <= 0) {
        e.preventDefault();
        showNotification('Please enter a valid amount', 'error');
        return;
    }
    
    if (amount > maxAmount) {
        e.preventDefault();
        showNotification(`Amount cannot exceed your available balance of $${maxAmount.toFixed(2)}`, 'error');
        return;
    }
    
    if (!walletAddress) {
        e.preventDefault();
        showNotification('Please enter a valid wallet address', 'error');
        return;
    }
    
    // Wallet address validation
    const trc20Regex = /^T[A-Za-z1-9]{33}$/;
    const bep20Regex = /^0x[a-fA-F0-9]{40}$/;
    
    if (!trc20Regex.test(walletAddress) && !bep20Regex.test(walletAddress)) {
        e.preventDefault();
        showNotification('Please enter a valid BEP20 or TRC20 wallet address', 'error');
        return;
    }
    
    // Confirmation
    if (!confirm(`Are you sure you want to withdraw $${amount.toFixed(2)} USDT to ${walletAddress}?`)) {
        e.preventDefault();
        return;
    }
    
    showLoading('withdrawBtn');
    // Form will submit normally after validation
});

// Auto-refresh withdrawal status every 30 seconds
setInterval(() => {
    const processingRows = document.querySelectorAll('tbody tr');
    processingRows.forEach(row => {
        const statusCell = row.querySelector('td:nth-child(4)');
        if (statusCell && statusCell.textContent.includes('Processing')) {
            // Could add AJAX call to check status
            console.log('Checking withdrawal status...');
        }
    });
}, 30000);

// Real-time balance update
function updateBalance() {
    // This would typically fetch from an API endpoint
    // For now, we'll just show the current balance
    console.log('Balance updated');
}

// Wallet address validation on input
document.getElementById('wallet_address').addEventListener('input', function(e) {
    const walletAddress = e.target.value;
    const trc20Regex = /^T[A-Za-z1-9]{33}$/;
    const bep20Regex = /^0x[a-fA-F0-9]{40}$/;
    
    if (walletAddress.length > 10) {
        if (trc20Regex.test(walletAddress)) {
            e.target.style.borderColor = 'var(--accent-green)';
            showNotification('Valid TRC20 address detected', 'success');
        } else if (bep20Regex.test(walletAddress)) {
            e.target.style.borderColor = 'var(--accent-green)';
            showNotification('Valid BEP20 address detected', 'success');
        } else {
            e.target.style.borderColor = '#ff4343';
        }
    } else {
        e.target.style.borderColor = '';
    }
});
</script>
{% endblock %}
