{% extends "base.html" %}

{% block title %}Dashboard - XCapital{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="section" style="padding-top: 8rem;">
    <div class="container">
        <!-- Welcome Header -->
        <div class="card" style="text-align: center; background: var(--gradient-dark); margin-bottom: 3rem;">
            <h1 style="margin-bottom: 1rem;">Welcome back, {{ current_user.username }}!</h1>
            <p style="font-size: 1.2rem; opacity: 0.9;">
                <i class="fas fa-clock"></i> Last login: {{ current_user.last_login.strftime('%B %d, %Y at %I:%M %p') if current_user.last_login else 'First time login' }}
            </p>
        </div>

        <!-- Quick Stats -->
        <div class="stats-grid">
            <div class="stat-card glow">
                <div class="stat-value" id="user-balance">{{ "%.2f"|format(current_user.balance) }}</div>
                <div class="stat-label">
                    <i class="fas fa-wallet"></i> Available Balance (USDT)
                </div>
            </div>
            
            <div class="stat-card glow">
                <div class="stat-value" id="daily-earnings">{{ "%.2f"|format(current_user.get_total_daily_earnings()) }}</div>
                <div class="stat-label">
                    <i class="fas fa-chart-line"></i> Daily Earnings (USDT)
                </div>
            </div>
            
            <div class="stat-card glow">
                <div class="stat-value" id="total-earned">{{ "%.2f"|format(current_user.total_earned) }}</div>
                <div class="stat-label">
                    <i class="fas fa-coins"></i> Total Earned (USDT)
                </div>
            </div>
            
            <div class="stat-card glow">
                <div class="stat-value">{{ "%.2f"|format(current_user.total_invested) }}</div>
                <div class="stat-label">
                    <i class="fas fa-gem"></i> Total Invested (USDT)
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <h3 style="margin-bottom: 2rem; text-align: center;">Quick Actions</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem;">
                <a href="{{ url_for('packages') }}" class="btn" style="text-align: center; padding: 2rem 1rem;">
                    <i class="fas fa-gem" style="font-size: 2rem; display: block; margin-bottom: 1rem;"></i>
                    Buy VIP Package
                </a>
                
                <a href="{{ url_for('withdraw') }}" class="btn btn-secondary" style="text-align: center; padding: 2rem 1rem;">
                    <i class="fas fa-money-bill-wave" style="font-size: 2rem; display: block; margin-bottom: 1rem;"></i>
                    Withdraw Funds
                </a>
                
                <a href="{{ url_for('referrals') }}" class="btn btn-secondary" style="text-align: center; padding: 2rem 1rem;">
                    <i class="fas fa-users" style="font-size: 2rem; display: block; margin-bottom: 1rem;"></i>
                    Invite Friends
                </a>
            </div>
        </div>

        <!-- Active Investments -->
        <div class="card">
            <h3 style="margin-bottom: 2rem;">
                <i class="fas fa-chart-pie"></i> Active Investments
            </h3>
            
            {% if active_investments %}
                <div class="table">
                    <table style="width: 100%;">
                        <thead>
                            <tr>
                                <th><i class="fas fa-gem"></i> Package</th>
                                <th><i class="fas fa-dollar-sign"></i> Amount</th>
                                <th><i class="fas fa-calendar"></i> Daily Return</th>
                                <th><i class="fas fa-coins"></i> Total Earned</th>
                                <th><i class="fas fa-clock"></i> Expires</th>
                                <th><i class="fas fa-signal"></i> Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for investment in active_investments %}
                            <tr>
                                <td>
                                    <span style="color: var(--primary-green); font-weight: bold;">
                                        {{ investment.package.name }}
                                    </span>
                                </td>
                                <td>${{ "%.2f"|format(investment.amount) }} USDT</td>
                                <td>
                                    <span style="color: var(--accent-green);">
                                        ${{ "%.2f"|format(investment.daily_return) }} USDT
                                    </span>
                                </td>
                                <td>${{ "%.2f"|format(investment.total_earned) }} USDT</td>
                                <td>{{ investment.expires_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <span style="color: var(--accent-green);">
                                        <i class="fas fa-check-circle"></i> Active
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div style="text-align: center; padding: 3rem; opacity: 0.7;">
                    <i class="fas fa-gem" style="font-size: 4rem; color: var(--primary-green); margin-bottom: 1rem;"></i>
                    <h4>No Active Investments</h4>
                    <p style="margin: 1rem 0;">Start your investment journey by purchasing a VIP package</p>
                    <a href="{{ url_for('packages') }}" class="btn">
                        <i class="fas fa-plus"></i> Buy Your First Package
                    </a>
                </div>
            {% endif %}
        </div>

        <!-- Recent Earnings -->
        <div class="card">
            <h3 style="margin-bottom: 2rem;">
                <i class="fas fa-history"></i> Recent Daily Earnings
            </h3>
            
            {% if recent_earnings %}
                <div class="table">
                    <table style="width: 100%;">
                        <thead>
                            <tr>
                                <th><i class="fas fa-calendar"></i> Date</th>
                                <th><i class="fas fa-gem"></i> Package</th>
                                <th><i class="fas fa-dollar-sign"></i> Amount</th>
                                <th><i class="fas fa-clock"></i> Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for earning in recent_earnings %}
                            <tr>
                                <td>{{ earning.earning_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <span style="color: var(--primary-green);">
                                        {{ earning.investment.package.name }}
                                    </span>
                                </td>
                                <td>
                                    <span style="color: var(--accent-green);">
                                        +${{ "%.2f"|format(earning.amount) }} USDT
                                    </span>
                                </td>
                                <td>{{ earning.created_at.strftime('%I:%M %p') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div style="text-align: center; padding: 3rem; opacity: 0.7;">
                    <i class="fas fa-chart-line" style="font-size: 4rem; color: var(--primary-green); margin-bottom: 1rem;"></i>
                    <h4>No Earnings Yet</h4>
                    <p>Your daily earnings will appear here once you have active investments</p>
                </div>
            {% endif %}
        </div>

        <!-- Referral Overview -->
        <div class="card">
            <h3 style="margin-bottom: 2rem;">
                <i class="fas fa-users"></i> Referral Network Overview
            </h3>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">{{ referral_stats.level1 }}</div>
                    <div class="stat-label">Level 1 Referrals</div>
                    <small style="color: var(--primary-green);">10% Commission</small>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value">{{ referral_stats.level2 }}</div>
                    <div class="stat-label">Level 2 Referrals</div>
                    <small style="color: var(--primary-green);">5% Commission</small>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value">{{ referral_stats.level3 }}</div>
                    <div class="stat-label">Level 3 Referrals</div>
                    <small style="color: var(--primary-green);">1% Commission</small>
                </div>
                
                <div class="stat-card">
                    <div class="stat-value">${{ "%.2f"|format(total_commissions) }}</div>
                    <div class="stat-label">Total Commissions (USDT)</div>
                    <small style="color: var(--accent-green);">Lifetime Earnings</small>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 2rem;">
                <a href="{{ url_for('referrals') }}" class="btn btn-secondary">
                    <i class="fas fa-external-link-alt"></i> View Detailed Referral Stats
                </a>
            </div>
        </div>

        <!-- Your Referral Link -->
        <div class="card" style="background: var(--gradient-dark);">
            <h3 style="text-align: center; margin-bottom: 2rem;">
                <i class="fas fa-link"></i> Your Referral Link
            </h3>
            
            <div style="display: flex; gap: 1rem; align-items: center; margin-bottom: 2rem;">
                <input type="text" 
                       value="{{ request.host_url }}register?ref={{ current_user.referral_code }}" 
                       id="referralLink" 
                       class="form-input" 
                       readonly
                       style="flex: 1;">
                <button class="btn copy-btn" data-target="referralLink">
                    <i class="fas fa-copy"></i> Copy
                </button>
            </div>
            
            <p style="text-align: center; opacity: 0.9;">
                Share this link with your friends and earn 10% commission on their investments for life!
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-refresh dashboard data every 30 seconds
startAutoRefresh();

// Initialize earnings chart if we have data
{% if recent_earnings %}
const earningsData = [
    {% for earning in recent_earnings|reverse %}
    {
        date: '{{ earning.earning_date.strftime("%m/%d") }}',
        amount: {{ earning.amount }}
    },
    {% endfor %}
];

const ctx = document.createElement('canvas');
ctx.id = 'earningsChart';
ctx.style.maxHeight = '300px';

// Add chart to recent earnings section if desired
// document.querySelector('.card:nth-of-type(4)').appendChild(ctx);
{% endif %}
</script>
{% endblock %}
