import os
import requests
import hashlib
import base64
import json
import logging
from datetime import datetime
from app import db
from models import Withdrawal, User

# Cryptomus API configuration
CRYPTOMUS_MERCHANT_ID = os.getenv("CRYPTOMUS_MERCHANT_ID")
CRYPTOMUS_PAYMENT_API_KEY = os.getenv("CRYPTOMUS_PAYMENT_API_KEY")
CRYPTOMUS_PAYOUT_API_KEY = os.getenv("CRYPTOMUS_PAYOUT_API_KEY")
CRYPTOMUS_BASE_URL = os.getenv("CRYPTOMUS_BASE_URL", "https://api.cryptomus.com/v1")

class CryptomusPaymentProcessor:
    """
    Cryptomus API handler for automatic cryptocurrency payments
    Features:
    - Free API for payments and payouts
    - Supports USDT TRC20 and other cryptocurrencies
    - Automatic conversion from fiat to crypto
    - 0% withdrawal fees
    """
    
    def __init__(self):
        self.merchant_id = CRYPTOMUS_MERCHANT_ID
        self.payment_api_key = CRYPTOMUS_PAYMENT_API_KEY
        self.payout_api_key = CRYPTOMUS_PAYOUT_API_KEY
        self.base_url = CRYPTOMUS_BASE_URL
    
    def _generate_sign(self, data, api_key):
        """Generate signature for Cryptomus API requests"""
        try:
            # Convert data to JSON string and encode to base64
            json_data = json.dumps(data, separators=(',', ':'))
            encoded_data = base64.b64encode(json_data.encode()).decode()
            
            # Create signature using MD5 hash
            sign_string = encoded_data + api_key
            signature = hashlib.md5(sign_string.encode()).hexdigest()
            
            return signature
        except Exception as e:
            logging.error(f"Error generating Cryptomus signature: {str(e)}")
            return None
    
    def create_payment_invoice(self, user_id, package_id, amount_usd):
        """
        Create a payment invoice for users to pay for VIP packages
        """
        try:
            order_id = f"XCAPITAL_USER_{user_id}_PKG_{package_id}_{int(datetime.now().timestamp())}"
            
            payment_data = {
                "amount": str(amount_usd),
                "currency": "USD",
                "order_id": order_id,
                "to_currency": "USDT",
                "network": "tron",
                "url_callback": f"{os.getenv('APP_URL', 'http://localhost:5000')}/webhook/cryptomus/payment",
                "url_success": f"{os.getenv('APP_URL', 'http://localhost:5000')}/payment/success",
                "url_return": f"{os.getenv('APP_URL', 'http://localhost:5000')}/packages",
                "lifetime": 3600,  # 1 hour
                "additional_data": f"VIP Package {package_id} for User {user_id}"
            }
            
            signature = self._generate_sign(payment_data, self.payment_api_key)
            if not signature:
                return None
            
            headers = {
                "merchant": self.merchant_id,
                "sign": signature,
                "Content-Type": "application/json"
            }
            
            response = requests.post(
                f"{self.base_url}/payment",
                json=payment_data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("state") == 0:
                    return result.get("result")
                else:
                    logging.error(f"Cryptomus payment creation error: {result}")
                    return None
            else:
                logging.error(f"Cryptomus API error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logging.error(f"Error creating Cryptomus payment: {str(e)}")
            return None
    
    def create_automatic_payout(self, user_id, amount_usd, wallet_address, withdrawal_id):
        """
        Create an automatic payout to user's wallet
        This is the key feature for automatic withdrawals
        """
        try:
            order_id = f"XCAPITAL_WITHDRAWAL_{withdrawal_id}_{int(datetime.now().timestamp())}"
            
            payout_data = {
                "amount": str(amount_usd),
                "currency": "USD",
                "to_currency": "USDT",
                "network": "tron",
                "order_id": order_id,
                "address": wallet_address,
                "is_subtract": True,  # Deduct fees from our balance, not user's amount
                "url_callback": f"{os.getenv('APP_URL', 'http://localhost:5000')}/webhook/cryptomus/payout",
                "from_currency": "USDT"  # Use USDT balance for conversion
            }
            
            signature = self._generate_sign(payout_data, self.payout_api_key)
            if not signature:
                return None
            
            headers = {
                "merchant": self.merchant_id,
                "sign": signature,
                "Content-Type": "application/json"
            }
            
            response = requests.post(
                f"{self.base_url}/payout",
                json=payout_data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("state") == 0:
                    return result.get("result")
                else:
                    logging.error(f"Cryptomus payout creation error: {result}")
                    return None
            else:
                logging.error(f"Cryptomus payout API error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logging.error(f"Error creating Cryptomus payout: {str(e)}")
            return None
    
    def get_balance(self):
        """Get merchant balance from Cryptomus"""
        try:
            data = {}
            signature = self._generate_sign(data, self.payment_api_key)
            
            headers = {
                "merchant": self.merchant_id,
                "sign": signature,
                "Content-Type": "application/json"
            }
            
            response = requests.post(
                f"{self.base_url}/balance",
                json=data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("state") == 0:
                    return result.get("result")
            
            return None
            
        except Exception as e:
            logging.error(f"Error getting Cryptomus balance: {str(e)}")
            return None
    
    def verify_webhook_signature(self, request_data, received_signature):
        """Verify webhook signature from Cryptomus"""
        try:
            # For webhooks, use payment API key
            calculated_signature = self._generate_sign(request_data, self.payment_api_key)
            return calculated_signature == received_signature
        except Exception as e:
            logging.error(f"Error verifying Cryptomus webhook: {str(e)}")
            return False

# Initialize the processor
cryptomus_processor = CryptomusPaymentProcessor()
