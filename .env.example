# XCapital - Environment Variables Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Database connection string - Choose one based on your database type:

# For PostgreSQL (recommended for production):
# DATABASE_URL=postgresql://username:password@localhost:5432/xcapital_db

# For MySQL:
# DATABASE_URL=mysql+pymysql://username:password@localhost:3306/xcapital_db

# For SQLite (development only):
# DATABASE_URL=sqlite:///xcapital.db

DATABASE_URL=

# =============================================================================
# FLASK APPLICATION CONFIGURATION
# =============================================================================
# Secret key for Flask sessions - Generate a strong random key
# You can generate one using: python -c "import secrets; print(secrets.token_hex(32))"
SESSION_SECRET=

# Application base URL (used for webhooks and redirects)
# For development:
# APP_URL=http://localhost:5000
# For production:
# APP_URL=https://yourdomain.com
APP_URL=http://localhost:5000

# =============================================================================
# PAYMENT PROCESSOR CONFIGURATION
# =============================================================================
# NowPayments.io API Configuration
# Sign up at https://nowpayments.io/ to get your API key
NOWPAYMENTS_API_KEY=

# Coinremitter API Configuration (Alternative payment processor)
# Sign up at https://coinremitter.com/ to get your credentials
COINREMITTER_API_KEY=
COINREMITTER_PASSWORD=

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================
# Flask environment (development/production)
FLASK_ENV=development

# Flask debug mode (true/false)
FLASK_DEBUG=true

# Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO
