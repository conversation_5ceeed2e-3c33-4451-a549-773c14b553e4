import os
import requests
import logging
from datetime import datetime
from app import db
from models import Payment, Investment, User, Commission, VIPPackage

# NowPayments.io API configuration
NOWPAYMENTS_API_KEY = os.getenv("NOWPAYMENTS_API_KEY", "3PRY7HJ-SNFMG8V-HZT3VX6-QW6Y5Y8")
NOWPAYMENTS_BASE_URL = "https://api.nowpayments.io/v1"

# Coinremitter API configuration
COINREMITTER_API_KEY = os.getenv("COINREMITTER_API_KEY", "demo_api_key")
COINREMITTER_PASSWORD = os.getenv("COINREMITTER_PASSWORD", "demo_password")
COINREMITTER_BASE_URL = "https://coinremitter.com/api/v3/USDTTRC20"

class PaymentProcessor:
    def __init__(self):
        self.nowpayments_headers = {
            "x-api-key": NOWPAYMENTS_API_KEY,
            "Content-Type": "application/json"
        }
    
    def create_payment(self, user_id, package_id, amount):
        """Create a payment using NowPayments.io"""
        try:
            payment_data = {
                "price_amount": amount,
                "price_currency": "USD",
                "pay_currency": "USDTTRC20",
                "order_id": f"USER_{user_id}_PKG_{package_id}_{int(datetime.now().timestamp())}",
                "order_description": f"VIP Package Purchase - Level {package_id}",
                "ipn_callback_url": f"{os.getenv('APP_URL', 'http://localhost:5000')}/webhook/payment",
                "success_url": f"{os.getenv('APP_URL', 'http://localhost:5000')}/payment/success",
                "cancel_url": f"{os.getenv('APP_URL', 'http://localhost:5000')}/payment/cancel"
            }
            
            response = requests.post(
                f"{NOWPAYMENTS_BASE_URL}/payment",
                json=payment_data,
                headers=self.nowpayments_headers
            )
            
            if response.status_code == 201:
                payment_info = response.json()
                
                # Save payment to database
                payment = Payment(
                    user_id=user_id,
                    package_id=package_id,
                    amount=amount,
                    payment_id=payment_info['payment_id'],
                    payment_url=payment_info['pay_url'],
                    status='pending'
                )
                db.session.add(payment)
                db.session.commit()
                
                return payment_info
            else:
                logging.error(f"NowPayments API error: {response.text}")
                return None
                
        except Exception as e:
            logging.error(f"Payment creation error: {str(e)}")
            return None
    
    def verify_payment(self, payment_id):
        """Verify payment status with NowPayments.io"""
        try:
            response = requests.get(
                f"{NOWPAYMENTS_BASE_URL}/payment/{payment_id}",
                headers=self.nowpayments_headers
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logging.error(f"Payment verification error: {response.text}")
                return None
                
        except Exception as e:
            logging.error(f"Payment verification error: {str(e)}")
            return None
    
    def process_confirmed_payment(self, payment_id):
        """Process a confirmed payment and create investment"""
        try:
            payment = Payment.query.filter_by(payment_id=payment_id).first()
            if not payment or payment.status == 'confirmed':
                return False
            
            user = User.query.get(payment.user_id)
            package = VIPPackage.query.get(payment.package_id)
            
            if not user or not package:
                return False
            
            # Create investment
            investment = Investment(
                user_id=user.id,
                package_id=package.id,
                amount=payment.amount,
                daily_return=package.daily_return
            )
            db.session.add(investment)
            
            # Update user stats
            user.total_invested += payment.amount
            
            # Update payment status
            payment.status = 'confirmed'
            payment.confirmed_at = datetime.utcnow()
            
            # Process referral commissions
            self.process_referral_commissions(user, investment)
            
            db.session.commit()
            logging.info(f"Investment created for user {user.id}, package {package.name}")
            return True
            
        except Exception as e:
            logging.error(f"Payment processing error: {str(e)}")
            db.session.rollback()
            return False
    
    def process_referral_commissions(self, user, investment):
        """Process 3-level referral commissions"""
        commission_rates = [0.10, 0.05, 0.01]  # 10%, 5%, 1%
        current_user = user
        
        for level in range(3):
            if current_user.referred_by:
                referrer = User.query.get(current_user.referred_by)
                if referrer and referrer.is_active:
                    commission_amount = investment.amount * commission_rates[level]
                    
                    # Create commission record
                    commission = Commission(
                        user_id=referrer.id,
                        from_user_id=user.id,
                        investment_id=investment.id,
                        level=level + 1,
                        rate=commission_rates[level],
                        amount=commission_amount
                    )
                    db.session.add(commission)
                    
                    # Add to referrer's balance
                    referrer.balance += commission_amount
                    
                    # Move to next level
                    current_user = referrer
                else:
                    break
            else:
                break

class WithdrawalProcessor:
    def __init__(self):
        self.coinremitter_data = {
            "api_key": COINREMITTER_API_KEY,
            "password": COINREMITTER_PASSWORD
        }
    
    def process_withdrawal(self, withdrawal_id):
        """Process withdrawal using Coinremitter API"""
        try:
            from models import Withdrawal
            withdrawal = Withdrawal.query.get(withdrawal_id)
            if not withdrawal or withdrawal.status != 'pending':
                return False
            
            # Update status to processing
            withdrawal.status = 'processing'
            db.session.commit()
            
            # Prepare withdrawal data
            withdrawal_data = {
                **self.coinremitter_data,
                "amount": str(withdrawal.amount),
                "address": withdrawal.wallet_address
            }
            
            response = requests.post(
                f"{COINREMITTER_BASE_URL}/send-money",
                data=withdrawal_data
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('flag') == 1:
                    # Success
                    withdrawal.status = 'completed'
                    withdrawal.transaction_id = result.get('data', {}).get('id')
                    withdrawal.processed_at = datetime.utcnow()
                    
                    # Update user balance
                    user = User.query.get(withdrawal.user_id)
                    user.total_withdrawn += withdrawal.amount
                    
                    db.session.commit()
                    logging.info(f"Withdrawal {withdrawal_id} completed successfully")
                    return True
                else:
                    # API returned error
                    withdrawal.status = 'failed'
                    withdrawal.notes = result.get('msg', 'Unknown error')
                    db.session.commit()
                    logging.error(f"Withdrawal {withdrawal_id} failed: {withdrawal.notes}")
                    return False
            else:
                # HTTP error
                withdrawal.status = 'failed'
                withdrawal.notes = f"HTTP Error: {response.status_code}"
                db.session.commit()
                logging.error(f"Withdrawal {withdrawal_id} failed with HTTP {response.status_code}")
                return False
                
        except Exception as e:
            # Exception occurred
            if withdrawal:
                withdrawal.status = 'failed'
                withdrawal.notes = f"Exception: {str(e)}"
                db.session.commit()
            logging.error(f"Withdrawal processing error: {str(e)}")
            return False
