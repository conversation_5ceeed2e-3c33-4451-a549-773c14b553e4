import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("=== Testing Environment Variables ===")
print(f"DATABASE_URL: {os.environ.get('DATABASE_URL')}")
print(f"SESSION_SECRET: {os.environ.get('SESSION_SECRET')}")
print(f"NOWPAYMENTS_API_KEY: {os.environ.get('NOWPAYMENTS_API_KEY')}")
print(f"CRYPTOMUS_PAYMENT_API_KEY: {os.environ.get('CRYPTOMUS_PAYMENT_API_KEY')[:20]}..." if os.environ.get('CRYPTOMUS_PAYMENT_API_KEY') else "Not set")

# Test database connection
try:
    from sqlalchemy import create_engine
    engine = create_engine(os.environ.get('DATABASE_URL'))
    with engine.connect() as conn:
        print("✅ Database connection successful!")
except Exception as e:
    print(f"❌ Database connection failed: {e}")
