import logging
from datetime import datetime, date
from apscheduler.schedulers.background import BackgroundScheduler
from app import app, db
from models import User, Investment, DailyEarning

def process_daily_earnings():
    """Process daily earnings for all active investments"""
    with app.app_context():
        try:
            today = date.today()
            logging.info(f"Processing daily earnings for {today}")
            
            # Get all active investments that haven't earned today
            active_investments = Investment.query.filter(
                Investment.is_active == True,
                Investment.expires_at > datetime.utcnow()
            ).all()
            
            earnings_processed = 0
            
            for investment in active_investments:
                # Check if already earned today
                existing_earning = DailyEarning.query.filter_by(
                    investment_id=investment.id,
                    earning_date=today
                ).first()
                
                if not existing_earning:
                    # Create daily earning
                    daily_earning = DailyEarning(
                        user_id=investment.user_id,
                        investment_id=investment.id,
                        amount=investment.daily_return,
                        earning_date=today
                    )
                    db.session.add(daily_earning)
                    
                    # Update user balance
                    user = User.query.get(investment.user_id)
                    user.balance += investment.daily_return
                    user.total_earned += investment.daily_return
                    
                    # Update investment stats
                    investment.total_earned += investment.daily_return
                    investment.last_earning_date = today
                    
                    earnings_processed += 1
            
            db.session.commit()
            logging.info(f"Processed {earnings_processed} daily earnings")
            
        except Exception as e:
            logging.error(f"Daily earnings processing error: {str(e)}")
            db.session.rollback()

def initialize_scheduler():
    """Initialize the background scheduler"""
    scheduler = BackgroundScheduler()
    
    # Schedule daily earnings processing at 8:00 AM EST
    scheduler.add_job(
        func=process_daily_earnings,
        trigger="cron",
        hour=8,
        minute=0,
        timezone='US/Eastern',
        id='daily_earnings'
    )
    
    scheduler.start()
    logging.info("Scheduler initialized - Daily earnings will process at 8:00 AM EST")
    
    return scheduler

# Initialize scheduler when module is imported
try:
    scheduler = initialize_scheduler()
except Exception as e:
    logging.error(f"Scheduler initialization failed: {str(e)}")
